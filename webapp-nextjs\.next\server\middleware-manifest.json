{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "8ad6d959824ba268d329f3bdcc87145c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "13e46b1a4c3a0c3399c58555c4f1a24f5504ffce14d2be498edf1d917518d3bb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dc7e019aefe409978464632e472622d07efe158ead6696a33cff63727d8fda90"}}}, "sortedMiddleware": ["/"], "functions": {}}