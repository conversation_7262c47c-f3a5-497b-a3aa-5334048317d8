{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/hooks/useCantiere.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Cantiere } from '@/types'\n\ninterface UseCantiereResult {\n  cantiereId: number | null\n  cantiere: Cantiere | null\n  isValidCantiere: boolean\n  isLoading: boolean\n  error: string | null\n  validateCantiere: (id: number | string) => boolean\n  clearError: () => void\n}\n\n/**\n * Hook personalizzato per la gestione robusta del cantiere selezionato\n * Gestisce validazione, errori e sincronizzazione con AuthContext\n */\nexport function useCantiere(): UseCantiereResult {\n  const { cantiere, isLoading: authLoading } = useAuth()\n  const [cantiereId, setCantiereId] = useState<number | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  // Funzione per validare un ID cantiere\n  const validateCantiere = (id: number | string): boolean => {\n    if (id === null || id === undefined) return false\n    \n    const numId = typeof id === 'string' ? parseInt(id, 10) : id\n    \n    if (isNaN(numId) || numId <= 0) {\n      console.warn('🏗️ useCantiere: ID cantiere non valido:', id)\n      return false\n    }\n    \n    return true\n  }\n\n  // Effetto per sincronizzare con AuthContext e localStorage\n  useEffect(() => {\n    if (authLoading) {\n      console.log('🏗️ useCantiere: Autenticazione in corso...')\n      return\n    }\n\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      let selectedId: number | null = null\n\n      // Priorità 1: Cantiere dal context di autenticazione (login cantiere diretto)\n      if (cantiere?.id_cantiere && validateCantiere(cantiere.id_cantiere)) {\n        selectedId = cantiere.id_cantiere\n        console.log('🏗️ useCantiere: Usando cantiere dal context (login cantiere):', selectedId)\n      } else {\n        // Priorità 2: Cantiere dal localStorage (cantiere_data per login cantiere)\n        const cantiereData = localStorage.getItem('cantiere_data')\n        if (cantiereData) {\n          try {\n            const parsedData = JSON.parse(cantiereData)\n            if (parsedData.id_cantiere && validateCantiere(parsedData.id_cantiere)) {\n              selectedId = parsedData.id_cantiere\n              console.log('🏗️ useCantiere: Usando cantiere da cantiere_data:', selectedId)\n            }\n          } catch (parseError) {\n            console.warn('🏗️ useCantiere: Errore parsing cantiere_data:', parseError)\n          }\n        }\n\n        // Priorità 3: Cantiere dal localStorage (selectedCantiereId per selezione manuale)\n        if (!selectedId) {\n          const storedId = localStorage.getItem('selectedCantiereId')\n          if (storedId && validateCantiere(storedId)) {\n            selectedId = parseInt(storedId, 10)\n            console.log('🏗️ useCantiere: Usando cantiere da selectedCantiereId:', selectedId)\n          }\n        }\n      }\n\n      if (selectedId) {\n        setCantiereId(selectedId)\n        console.log('🏗️ useCantiere: Cantiere valido impostato:', selectedId)\n      } else {\n        console.warn('🏗️ useCantiere: Nessun cantiere valido trovato')\n        setCantiereId(null)\n        setError('Nessun cantiere selezionato. Seleziona un cantiere per continuare.')\n      }\n    } catch (err) {\n      console.error('🏗️ useCantiere: Errore nella gestione cantiere:', err)\n      setError('Errore nella gestione del cantiere selezionato.')\n      setCantiereId(null)\n    } finally {\n      setIsLoading(false)\n    }\n  }, [cantiere, authLoading])\n\n  const clearError = () => setError(null)\n\n  return {\n    cantiereId,\n    cantiere,\n    isValidCantiere: cantiereId !== null && cantiereId > 0,\n    isLoading,\n    error,\n    validateCantiere,\n    clearError\n  }\n}\n\n/**\n * Hook semplificato che restituisce solo l'ID del cantiere valido o null\n */\nexport function useCantiereId(): number | null {\n  const { cantiereId } = useCantiere()\n  return cantiereId\n}\n\n/**\n * Hook che forza la presenza di un cantiere valido\n * Lancia un errore se non c'è un cantiere selezionato\n */\nexport function useRequiredCantiere(): { cantiereId: number; cantiere: Cantiere | null } {\n  const { cantiereId, cantiere, isLoading, error } = useCantiere()\n\n  if (isLoading) {\n    throw new Error('Caricamento cantiere in corso...')\n  }\n\n  if (error) {\n    throw new Error(error)\n  }\n\n  if (!cantiereId || cantiereId <= 0) {\n    throw new Error('Nessun cantiere selezionato. Seleziona un cantiere per continuare.')\n  }\n\n  return { cantiereId, cantiere }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;AAHA;;;AAoBO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,uCAAuC;IACvC,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO,QAAQ,OAAO,WAAW,OAAO;QAE5C,MAAM,QAAQ,OAAO,OAAO,WAAW,SAAS,IAAI,MAAM;QAE1D,IAAI,MAAM,UAAU,SAAS,GAAG;YAC9B,QAAQ,IAAI,CAAC,4CAA4C;YACzD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,2DAA2D;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,aAAa;gBACf,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,aAAa;YACb,SAAS;YAET,IAAI;gBACF,IAAI,aAA4B;gBAEhC,8EAA8E;gBAC9E,IAAI,UAAU,eAAe,iBAAiB,SAAS,WAAW,GAAG;oBACnE,aAAa,SAAS,WAAW;oBACjC,QAAQ,GAAG,CAAC,kEAAkE;gBAChF,OAAO;oBACL,2EAA2E;oBAC3E,MAAM,eAAe,aAAa,OAAO,CAAC;oBAC1C,IAAI,cAAc;wBAChB,IAAI;4BACF,MAAM,aAAa,KAAK,KAAK,CAAC;4BAC9B,IAAI,WAAW,WAAW,IAAI,iBAAiB,WAAW,WAAW,GAAG;gCACtE,aAAa,WAAW,WAAW;gCACnC,QAAQ,GAAG,CAAC,sDAAsD;4BACpE;wBACF,EAAE,OAAO,YAAY;4BACnB,QAAQ,IAAI,CAAC,kDAAkD;wBACjE;oBACF;oBAEA,mFAAmF;oBACnF,IAAI,CAAC,YAAY;wBACf,MAAM,WAAW,aAAa,OAAO,CAAC;wBACtC,IAAI,YAAY,iBAAiB,WAAW;4BAC1C,aAAa,SAAS,UAAU;4BAChC,QAAQ,GAAG,CAAC,2DAA2D;wBACzE;oBACF;gBACF;gBAEA,IAAI,YAAY;oBACd,cAAc;oBACd,QAAQ,GAAG,CAAC,+CAA+C;gBAC7D,OAAO;oBACL,QAAQ,IAAI,CAAC;oBACb,cAAc;oBACd,SAAS;gBACX;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,oDAAoD;gBAClE,SAAS;gBACT,cAAc;YAChB,SAAU;gBACR,aAAa;YACf;QACF;gCAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,aAAa,IAAM,SAAS;IAElC,OAAO;QACL;QACA;QACA,iBAAiB,eAAe,QAAQ,aAAa;QACrD;QACA;QACA;QACA;IACF;AACF;GA1FgB;;QAC+B,kIAAA,CAAA,UAAO;;;AA8F/C,SAAS;;IACd,MAAM,EAAE,UAAU,EAAE,GAAG;IACvB,OAAO;AACT;IAHgB;;QACS;;;AAQlB,SAAS;;IACd,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IAEnD,IAAI,WAAW;QACb,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,OAAO;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,CAAC,cAAc,cAAc,GAAG;QAClC,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QAAE;QAAY;IAAS;AAChC;IAhBgB;;QACqC", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/utils/softColors.ts"], "sourcesContent": ["/**\n * PALETTE COLORI CENTRALIZZATA v3.0 - CABLYS\n * Versione ristretta e professionale per eliminare l'effetto \"Arlecchino\"\n * Data: 30 Giugno 2025\n */\n\n// COLORI PRIMARI E NEUTRI (Uso rigoroso e limitato)\nexport const CABLYS_COLORS = {\n  // COLORE PRIMARIO - Blu CABLYS (Unico colore per azioni e elementi cliccabili)\n  PRIMARY: {\n    bg: 'bg-blue-50',           // #eff6ff - Sfondo molto chiaro per hover\n    text: 'text-blue-600',      // #2563eb - Testo/bordi (equivalente a #007bff)\n    border: 'border-blue-300',  // #93c5fd - Bordi\n    hover: 'hover:bg-blue-50',  // #eff6ff - Hover state\n    active: 'hover:border-blue-400', // #60a5fa - Bordo attivo\n    hex: '#007bff'              // Blu primario CABLYS\n  },\n\n  // COLORI NEUTRI - Grigio (Per testo, disabilitato, non disponibile)\n  NEUTRAL: {\n    text_dark: 'text-gray-800',     // #1f2937 - Testo principale\n    text_medium: 'text-gray-600',   // #4b5563 - Testo secondario\n    text_light: 'text-gray-500',    // #6b7280 - Testo disabilitato\n    bg_white: 'bg-white',           // #ffffff - Sfondo bianco\n    bg_light: 'bg-gray-50',         // #f9fafb - Sfondo hover leggero\n    border: 'border-gray-300',      // #d1d5db - Bordi divisori\n    hex_dark: '#343A40',            // Grigio antracite per massima leggibilità\n    hex_medium: '#6c757d',          // Grigio medio\n    hex_light: '#DEE2E6'            // Grigio chiaro per bordi\n  },\n\n  // COLORI DI STATO (Desaturati e pastello per badge informativi)\n  STATUS: {\n    // Successo/Positivo - Verde desaturato\n    SUCCESS: {\n      bg: 'bg-green-50',          // #f0fdf4 - Verde molto pallido\n      text: 'text-green-700',     // #15803d - Verde scuro per testo\n      border: 'border-green-200', // #bbf7d0 - Bordo verde chiaro\n      hex: '#28A745'              // Verde pulito\n    },\n\n    // Avviso/Attenzione - Arancione desaturato\n    WARNING: {\n      bg: 'bg-orange-50',         // #fff7ed - Arancione molto pallido\n      text: 'text-orange-700',    // #c2410c - Arancione scuro per testo\n      border: 'border-orange-200', // #fed7aa - Bordo arancione chiaro\n      hex: '#FD7E14'              // Arancione energico\n    },\n\n    // Errore/Pericolo - Rosso desaturato\n    ERROR: {\n      bg: 'bg-red-50',            // #fef2f2 - Rosso molto pallido\n      text: 'text-red-700',       // #b91c1c - Rosso scuro per testo\n      border: 'border-red-200',   // #fecaca - Bordo rosso chiaro\n      hex: '#DC3545'              // Rosso chiaro e diretto\n    }\n  }\n}\n\n/**\n * MAPPATURE STATI BOBINE - Palette Centralizzata v3.0\n * Usa solo colori di stato desaturati per badge informativi\n */\nexport const BOBINA_COLORS = {\n  DISPONIBILE: CABLYS_COLORS.STATUS.SUCCESS,\n  IN_USO: CABLYS_COLORS.STATUS.WARNING,      // Arancione per \"in uso\"\n  TERMINATA: CABLYS_COLORS.NEUTRAL,          // Grigio neutro\n  OVER: CABLYS_COLORS.STATUS.ERROR,          // Rosso per \"over\"\n  VUOTA: CABLYS_COLORS.NEUTRAL,              // Grigio neutro per vuota\n  ERRORE: CABLYS_COLORS.STATUS.ERROR\n}\n\n/**\n * MAPPATURE STATI CAVI - Palette Centralizzata v3.0\n * Usa solo colori di stato desaturati per badge informativi\n */\nexport const CAVO_COLORS = {\n  DA_INSTALLARE: CABLYS_COLORS.NEUTRAL,      // Grigio neutro\n  INSTALLATO: CABLYS_COLORS.STATUS.SUCCESS,  // Verde per successo\n  COLLEGATO_PARTENZA: CABLYS_COLORS.STATUS.WARNING, // Arancione per parziale\n  COLLEGATO_ARRIVO: CABLYS_COLORS.STATUS.WARNING,   // Arancione per parziale\n  COLLEGATO: CABLYS_COLORS.STATUS.SUCCESS,   // Verde per completo\n  CERTIFICATO: CABLYS_COLORS.STATUS.SUCCESS, // Verde per certificato\n  SPARE: CABLYS_COLORS.STATUS.WARNING,       // Arancione per spare\n  ERRORE: CABLYS_COLORS.STATUS.ERROR\n}\n\n/**\n * MAPPATURE STATI COMANDE - Palette Centralizzata v3.0\n * Usa solo colori di stato desaturati per badge informativi\n */\nexport const COMANDA_COLORS = {\n  ATTIVA: CABLYS_COLORS.STATUS.SUCCESS,\n  COMPLETATA: CABLYS_COLORS.STATUS.SUCCESS,\n  ANNULLATA: CABLYS_COLORS.NEUTRAL,\n  IN_CORSO: CABLYS_COLORS.STATUS.WARNING,\n  ERRORE: CABLYS_COLORS.STATUS.ERROR\n}\n\n/**\n * FUNZIONI UTILITY - Palette Centralizzata v3.0\n * Restituiscono classi CSS per badge informativi (NON cliccabili)\n */\n\nexport const getBobinaColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase() as keyof typeof BOBINA_COLORS\n  const color = BOBINA_COLORS[normalizedStato] || BOBINA_COLORS.ERRORE\n\n  return {\n    // Badge informativo a pillola (senza hover - NON cliccabile)\n    badge: `${color.bg} ${color.text} rounded-full px-3 py-1 text-xs font-medium`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hex: color.hex\n  }\n}\n\nexport const getCavoColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase().replace(/\\s+/g, '_') as keyof typeof CAVO_COLORS\n  const color = CAVO_COLORS[normalizedStato] || CAVO_COLORS.ERRORE\n\n  return {\n    // Badge informativo a pillola (senza hover - NON cliccabile)\n    badge: `${color.bg} ${color.text} rounded-full px-3 py-1 text-xs font-medium`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hex: color.hex\n  }\n}\n\n/**\n * UTILITY PER PULSANTI UNIFORMI - Stile Moderno Pulito\n * UN SOLO TIPO DI PULSANTE per tutta l'interfaccia - STILE COMPATTO MODERNO\n */\nexport const getUniformButtonClasses = () => {\n  return {\n    // Pulsante uniforme moderno - solo bordo inferiore prima dell'hover (senza effetti sovrapposti)\n    button: `inline-flex items-center justify-center gap-1 px-3 py-2 text-xs font-medium bg-white text-gray-800 border-b-2 border-[#315cfd] rounded-full cursor-pointer min-w-[4rem] h-8 transition-colors duration-300 hover:border-2 hover:bg-[#315cfd] hover:text-white`,\n    text: 'text-gray-800',\n    border: 'border-b-[#315cfd]',\n    hover: 'hover:bg-[#315cfd] hover:text-white hover:border-2'\n  }\n}\n\n/**\n * UTILITY PER PULSANTI AZIONE - Palette Centralizzata v3.3 MINIMAL\n * Mantiene retrocompatibilità ma usa lo stile uniforme\n */\nexport const getPrimaryActionClasses = () => {\n  return getUniformButtonClasses()\n}\n\n/**\n * UTILITY PER PULSANTI AZIONE SECONDARI - Palette Centralizzata v3.3 MINIMAL\n * Mantiene retrocompatibilità ma usa lo stile uniforme\n */\nexport const getSecondaryActionClasses = () => {\n  return getUniformButtonClasses()\n}\n\n/**\n * UTILITY PER BADGE CLICCABILI - Palette Centralizzata v3.3 MINIMAL\n * Mantiene retrocompatibilità ma usa lo stile uniforme\n */\nexport const getClickableBadgeClasses = () => {\n  return getUniformButtonClasses()\n}\n\n/**\n * UTILITY PER ELEMENTI NON DISPONIBILI - Palette Centralizzata v3.0\n * Restituisce classi per testo statico non cliccabile\n */\nexport const getUnavailableClasses = () => {\n  return {\n    // Testo grigio statico (NON cliccabile)\n    text: `inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ${CABLYS_COLORS.NEUTRAL.text_light}`,\n    color: CABLYS_COLORS.NEUTRAL.text_light\n  }\n}\n\nexport const getComandaColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase().replace(/\\s+/g, '_') as keyof typeof COMANDA_COLORS\n  const color = COMANDA_COLORS[normalizedStato] || COMANDA_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\n/**\n * UTILITY PER PERCENTUALI DI PROGRESSO - Palette Centralizzata v3.0\n */\nexport const getProgressColor = (percentage: number) => {\n  if (percentage >= 90) return CABLYS_COLORS.STATUS.SUCCESS\n  if (percentage >= 70) return CABLYS_COLORS.STATUS.SUCCESS\n  if (percentage >= 50) return CABLYS_COLORS.STATUS.WARNING\n  if (percentage >= 30) return CABLYS_COLORS.STATUS.WARNING\n  return CABLYS_COLORS.STATUS.ERROR\n}\n\n/**\n * MAPPATURE PRIORITÀ - Palette Centralizzata v3.0\n */\nexport const PRIORITY_COLORS = {\n  ALTA: CABLYS_COLORS.STATUS.ERROR,\n  MEDIA: CABLYS_COLORS.STATUS.WARNING,\n  BASSA: CABLYS_COLORS.NEUTRAL,\n  NORMALE: CABLYS_COLORS.NEUTRAL\n}\n\nexport const getPriorityColorClasses = (priority: string) => {\n  const normalizedPriority = priority?.toUpperCase() as keyof typeof PRIORITY_COLORS\n  const color = PRIORITY_COLORS[normalizedPriority] || PRIORITY_COLORS.NORMALE\n\n  return {\n    // Badge informativo a pillola (senza hover - NON cliccabile)\n    badge: `${color.bg} ${color.text} rounded-full px-3 py-1 text-xs font-medium`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hex: color.hex\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,oDAAoD;;;;;;;;;;;;;;;;;;AAC7C,MAAM,gBAAgB;IAC3B,+EAA+E;IAC/E,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,KAAK,UAAuB,sBAAsB;IACpD;IAEA,oEAAoE;IACpE,SAAS;QACP,WAAW;QACX,aAAa;QACb,YAAY;QACZ,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,UAAqB,0BAA0B;IAC5D;IAEA,gEAAgE;IAChE,QAAQ;QACN,uCAAuC;QACvC,SAAS;YACP,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,KAAK,UAAuB,eAAe;QAC7C;QAEA,2CAA2C;QAC3C,SAAS;YACP,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,KAAK,UAAuB,qBAAqB;QACnD;QAEA,qCAAqC;QACrC,OAAO;YACL,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,KAAK,UAAuB,yBAAyB;QACvD;IACF;AACF;AAMO,MAAM,gBAAgB;IAC3B,aAAa,cAAc,MAAM,CAAC,OAAO;IACzC,QAAQ,cAAc,MAAM,CAAC,OAAO;IACpC,WAAW,cAAc,OAAO;IAChC,MAAM,cAAc,MAAM,CAAC,KAAK;IAChC,OAAO,cAAc,OAAO;IAC5B,QAAQ,cAAc,MAAM,CAAC,KAAK;AACpC;AAMO,MAAM,cAAc;IACzB,eAAe,cAAc,OAAO;IACpC,YAAY,cAAc,MAAM,CAAC,OAAO;IACxC,oBAAoB,cAAc,MAAM,CAAC,OAAO;IAChD,kBAAkB,cAAc,MAAM,CAAC,OAAO;IAC9C,WAAW,cAAc,MAAM,CAAC,OAAO;IACvC,aAAa,cAAc,MAAM,CAAC,OAAO;IACzC,OAAO,cAAc,MAAM,CAAC,OAAO;IACnC,QAAQ,cAAc,MAAM,CAAC,KAAK;AACpC;AAMO,MAAM,iBAAiB;IAC5B,QAAQ,cAAc,MAAM,CAAC,OAAO;IACpC,YAAY,cAAc,MAAM,CAAC,OAAO;IACxC,WAAW,cAAc,OAAO;IAChC,UAAU,cAAc,MAAM,CAAC,OAAO;IACtC,QAAQ,cAAc,MAAM,CAAC,KAAK;AACpC;AAOO,MAAM,wBAAwB,CAAC;IACpC,MAAM,kBAAkB,OAAO;IAC/B,MAAM,QAAQ,aAAa,CAAC,gBAAgB,IAAI,cAAc,MAAM;IAEpE,OAAO;QACL,6DAA6D;QAC7D,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,2CAA2C,CAAC;QAC7E,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,kBAAkB,OAAO,cAAc,QAAQ,QAAQ;IAC7D,MAAM,QAAQ,WAAW,CAAC,gBAAgB,IAAI,YAAY,MAAM;IAEhE,OAAO;QACL,6DAA6D;QAC7D,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,2CAA2C,CAAC;QAC7E,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,KAAK,MAAM,GAAG;IAChB;AACF;AAMO,MAAM,0BAA0B;IACrC,OAAO;QACL,gGAAgG;QAChG,QAAQ,CAAC,6PAA6P,CAAC;QACvQ,MAAM;QACN,QAAQ;QACR,OAAO;IACT;AACF;AAMO,MAAM,0BAA0B;IACrC,OAAO;AACT;AAMO,MAAM,4BAA4B;IACvC,OAAO;AACT;AAMO,MAAM,2BAA2B;IACtC,OAAO;AACT;AAMO,MAAM,wBAAwB;IACnC,OAAO;QACL,wCAAwC;QACxC,MAAM,CAAC,6DAA6D,EAAE,cAAc,OAAO,CAAC,UAAU,EAAE;QACxG,OAAO,cAAc,OAAO,CAAC,UAAU;IACzC;AACF;AAEO,MAAM,yBAAyB,CAAC;IACrC,MAAM,kBAAkB,OAAO,cAAc,QAAQ,QAAQ;IAC7D,MAAM,QAAQ,cAAc,CAAC,gBAAgB,IAAI,eAAe,MAAM;IAEtE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAKO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,cAAc,IAAI,OAAO,cAAc,MAAM,CAAC,OAAO;IACzD,IAAI,cAAc,IAAI,OAAO,cAAc,MAAM,CAAC,OAAO;IACzD,IAAI,cAAc,IAAI,OAAO,cAAc,MAAM,CAAC,OAAO;IACzD,IAAI,cAAc,IAAI,OAAO,cAAc,MAAM,CAAC,OAAO;IACzD,OAAO,cAAc,MAAM,CAAC,KAAK;AACnC;AAKO,MAAM,kBAAkB;IAC7B,MAAM,cAAc,MAAM,CAAC,KAAK;IAChC,OAAO,cAAc,MAAM,CAAC,OAAO;IACnC,OAAO,cAAc,OAAO;IAC5B,SAAS,cAAc,OAAO;AAChC;AAEO,MAAM,0BAA0B,CAAC;IACtC,MAAM,qBAAqB,UAAU;IACrC,MAAM,QAAQ,eAAe,CAAC,mBAAmB,IAAI,gBAAgB,OAAO;IAE5E,OAAO;QACL,6DAA6D;QAC7D,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,2CAA2C,CAAC;QAC7E,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,KAAK,MAAM,GAAG;IAChB;AACF", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/cavi/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRout<PERSON> } from 'next/navigation'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCantiere } from '@/hooks/useCantiere'\nimport { CantiereErrorBoundary } from '@/components/cantiere/CantiereErrorBoundary'\nimport { caviApi } from '@/lib/api'\nimport { Cavo } from '@/types'\nimport CaviTable from '@/components/cavi/CaviTable'\nimport CaviStatistics from '@/components/cavi/CaviStatistics'\nimport InserisciMetriDialog from '@/components/cavi/InserisciMetriDialog'\nimport CollegamentiDialogSimple from '@/components/cavi/CollegamentiDialogSimple'\n// Import dei nuovi componenti modali migliorati\nimport {\n  DisconnectCableModal,\n  GeneratePdfModal,\n  CertificationModal,\n  CertificationErrorModal,\n  SuccessToast\n} from '@/components/cavi/modals/CableActionModals'\nimport {\n  UnifiedCableBobbinModal\n} from '@/components/cavi/modals/BobinaManagementModals'\n// Import CSS per i modali migliorati\nimport '@/components/cavi/modals/enhanced-modals.css'\n// Import del vecchio dialog per InserisciMetri (temporaneo)\nimport InserisciMetriDialogOld from '@/components/cavi/InserisciMetriDialog'\nimport CreaComandaDialog from '@/components/cavi/CreaComandaDialog'\nimport ImportExcelDialog from '@/components/cavi/ImportExcelDialog'\n// Import dei dialog per gestione cavi\nimport AggiungiCavoDialog from '@/components/cavi/AggiungiCavoDialog'\nimport ModificaCavoDialog from '@/components/cavi/ModificaCavoDialog'\nimport EliminaCavoDialog from '@/components/cavi/EliminaCavoDialog'\nimport ExportDataDialog from '@/components/cavi/ExportDataDialog'\n// import { useToast } from '@/hooks/use-toast'\nimport {\n  Package,\n  AlertCircle,\n  Loader2\n} from 'lucide-react'\n\ninterface DashboardStats {\n  totali: number\n  installati: number\n  collegati: number\n  certificati: number\n  percentualeInstallazione: number\n  percentualeCollegamento: number\n  percentualeCertificazione: number\n  metriTotali: number\n  metriInstallati: number\n  metriCollegati: number\n  metriCertificati: number\n}\n\nexport default function CaviPage() {\n  const { user, isAuthenticated, isLoading: authLoading } = useAuth()\n  const { cantiereId, cantiere, isValidCantiere, isLoading: cantiereLoading, error: cantiereError } = useCantiere()\n  const router = useRouter()\n\n\n\n  // Sistema toast semplice\n  const toast = ({ title, description, variant }: { title: string, description: string, variant?: string }) => {\n    // TODO: Implementare sistema toast visuale\n  }\n  const [cavi, setCavi] = useState<Cavo[]>([])\n  const [caviSpare, setCaviSpare] = useState<Cavo[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [selectedCavi, setSelectedCavi] = useState<string[]>([])\n  const [selectionEnabled, setSelectionEnabled] = useState(true)\n  const [filteredCavi, setFilteredCavi] = useState<Cavo[]>([])\n  const [revisioneCorrente, setRevisioneCorrente] = useState<string>('')\n\n  // Update filtered cavi when main cavi change\n  useEffect(() => {\n    setFilteredCavi(cavi)\n  }, [cavi])\n\n  // Stati per i dialoghi\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  // Stati per i dialog di gestione cavi\n  const [aggiungiCavoDialog, setAggiungiCavoDialog] = useState(false)\n  const [modificaCavoDialog, setModificaCavoDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n  const [eliminaCavoDialog, setEliminaCavoDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  // Stato per la modale unificata\n  const [unifiedModal, setUnifiedModal] = useState<{\n    open: boolean\n    mode: 'aggiungi_metri' | 'modifica_bobina' | null\n    cavo: Cavo | null\n  }>({ open: false, mode: null, cavo: null })\n\n  const [collegamentiDialog, setCollegamentiDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  // Stati per i nuovi modali migliorati\n  const [disconnectModal, setDisconnectModal] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [generatePdfModal, setGeneratePdfModal] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [certificationModal, setCertificationModal] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [certificationErrorModal, setCertificationErrorModal] = useState<{\n    open: boolean\n    cavo: Cavo | null\n    error: string\n  }>({ open: false, cavo: null, error: '' })\n\n  const [successToast, setSuccessToast] = useState<{\n    visible: boolean\n    message: string\n  }>({ visible: false, message: '' })\n\n  const [creaComandaDialog, setCreaComandaDialog] = useState<{\n    open: boolean\n    tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'\n  }>({ open: false })\n\n  const [importExcelDialog, setImportExcelDialog] = useState<{\n    open: boolean\n    tipo?: 'cavi' | 'bobine'\n  }>({ open: false })\n\n  const [exportDataDialog, setExportDataDialog] = useState(false)\n  const [stats, setStats] = useState<DashboardStats>({\n    totali: 0,\n    installati: 0,\n    collegati: 0,\n    certificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriCollegati: 0,\n    metriCertificati: 0\n  })\n\n  useEffect(() => {\n    if (!authLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, authLoading, router])\n\n  // Crea oggetto cantiere per il dialog\n  const cantiereForDialog = cantiere || (cantiereId && cantiereId > 0 ? {\n    id_cantiere: cantiereId,\n    commessa: `Cantiere ${cantiereId}`\n  } : null)\n\n  // Carica i cavi dal backend - MIGLIORATO con nuovo hook\n  useEffect(() => {\n    if (isValidCantiere && cantiereId && cantiereId > 0 && !cantiereLoading) {\n\n      loadCavi()\n      loadRevisioneCorrente()\n    } else if (!cantiereLoading && !isValidCantiere) {\n\n      setCavi([])\n      setCaviSpare([])\n      setError(cantiereError || 'Nessun cantiere selezionato')\n    }\n  }, [cantiereId, isValidCantiere, cantiereLoading, cantiereError])\n\n  const loadRevisioneCorrente = async () => {\n    try {\n      const response = await fetch(`http://localhost:8001/api/cavi/${cantiereId}/revisione-corrente`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        setRevisioneCorrente(data.revisione_corrente || '00')\n      } else {\n        setRevisioneCorrente('00')\n      }\n    } catch (error) {\n      setRevisioneCorrente('00')\n    }\n  }\n\n  const loadCavi = async () => {\n    try {\n      setLoading(true)\n      setError('')\n\n      // Prima prova con l'API normale\n      try {\n        const data = await caviApi.getCavi(cantiereId!)\n\n        // Separa cavi attivi e spare\n        const caviAttivi = data.filter((cavo: Cavo) => !cavo.spare)\n        const caviSpareFiltered = data.filter((cavo: Cavo) => cavo.spare)\n\n        setCavi(caviAttivi)\n        setCaviSpare(caviSpareFiltered)\n\n        // Calcola statistiche\n        calculateStats(caviAttivi)\n\n      } catch (apiError: any) {\n        throw apiError\n      }\n\n    } catch (error: any) {\n      setError(`Errore nel caricamento dei cavi: ${error.response?.data?.detail || error.message}`)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const calculateStats = (caviData: Cavo[]) => {\n    const totali = caviData.length\n    const installati = caviData.filter(c => (c.metri_posati || c.metratura_reale || 0) > 0).length\n    const collegati = caviData.filter(c => (c.collegamento || c.collegamenti) === 3).length // 3 = collegato\n    const certificati = caviData.filter(c => c.certificato).length\n\n    const metriTotali = caviData.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)\n    const metriInstallati = caviData.reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n    const metriCollegati = caviData.filter(c => c.collegamento === 3).reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n    const metriCertificati = caviData.filter(c => c.certificato).reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n\n    setStats({\n      totali,\n      installati,\n      collegati,\n      certificati,\n      percentualeInstallazione: totali > 0 ? Math.round((installati / totali) * 100) : 0,\n      percentualeCollegamento: totali > 0 ? Math.round((collegati / totali) * 100) : 0,\n      percentualeCertificazione: totali > 0 ? Math.round((certificati / totali) * 100) : 0,\n      metriTotali,\n      metriInstallati,\n      metriCollegati,\n      metriCertificati\n    })\n  }\n\n  // Gestione azioni sui cavi con i nuovi modali migliorati\n  const handleStatusAction = (cavo: Cavo, action: string, label?: string) => {\n\n    switch (action) {\n      case 'insert_meters':\n        // Usa la modale unificata in modalità aggiungi_metri\n        setUnifiedModal({ open: true, mode: 'aggiungi_metri', cavo })\n        break\n      case 'modify_reel':\n        // Usa la modale unificata in modalità modifica_bobina\n        setUnifiedModal({ open: true, mode: 'modifica_bobina', cavo })\n        break\n      case 'view_command':\n        toast({\n          title: \"Visualizza Comanda\",\n          description: `Apertura comanda ${label} per cavo ${cavo.id_cavo}`,\n        })\n        break\n      case 'connect_cable':\n      case 'connect_arrival':\n      case 'connect_departure':\n      case 'manage_connections':\n        // Usa il modal specifico per gestione collegamenti\n        setCollegamentiDialog({ open: true, cavo })\n        break\n      case 'disconnect_cable':\n        // Usa il modal specifico per gestione collegamenti (scollegamento)\n        setCollegamentiDialog({ open: true, cavo })\n        break\n      case 'create_certificate':\n        // Usa il nuovo modal di certificazione\n        setCertificationModal({ open: true, cavo })\n        break\n      case 'generate_pdf':\n        // Usa il nuovo modal di generazione PDF\n        setGeneratePdfModal({ open: true, cavo })\n        break\n    }\n  }\n\n  const handleContextMenuAction = (cavo: Cavo, action: string) => {\n\n    switch (action) {\n      case 'view_details':\n        toast({\n          title: \"Visualizza Dettagli\",\n          description: `Apertura dettagli per cavo ${cavo.id_cavo}`,\n        })\n        break\n      case 'edit':\n        setModificaCavoDialog({ open: true, cavo })\n        break\n      case 'delete':\n        setEliminaCavoDialog({ open: true, cavo })\n        break\n      case 'add_new':\n        setAggiungiCavoDialog(true)\n        break\n      case 'select':\n        const isSelected = selectedCavi.includes(cavo.id_cavo)\n        if (isSelected) {\n          setSelectedCavi(selectedCavi.filter(id => id !== cavo.id_cavo))\n          toast({\n            title: \"Cavo Deselezionato\",\n            description: `Cavo ${cavo.id_cavo} deselezionato`,\n          })\n        } else {\n          setSelectedCavi([...selectedCavi, cavo.id_cavo])\n          toast({\n            title: \"Cavo Selezionato\",\n            description: `Cavo ${cavo.id_cavo} selezionato`,\n          })\n        }\n        break\n      case 'copy_id':\n        navigator.clipboard.writeText(cavo.id_cavo)\n        toast({\n          title: \"ID Copiato\",\n          description: `ID cavo ${cavo.id_cavo} copiato negli appunti`,\n        })\n        break\n      case 'copy_details':\n        const details = `ID: ${cavo.id_cavo}, Tipologia: ${cavo.tipologia}, Formazione: ${cavo.formazione || cavo.sezione}, Metri: ${cavo.metri_teorici}`\n        navigator.clipboard.writeText(details)\n        toast({\n          title: \"Dettagli Copiati\",\n          description: \"Dettagli cavo copiati negli appunti\",\n        })\n        break\n      case 'add_to_command':\n        toast({\n          title: \"Aggiungi a Comanda\",\n          description: \"Funzione aggiunta a comanda in sviluppo\",\n        })\n        break\n      case 'remove_from_command':\n        toast({\n          title: \"Rimuovi da Comanda\",\n          description: \"Funzione rimozione da comanda in sviluppo\",\n        })\n        break\n      case 'create_command_posa':\n        setCreaComandaDialog({ open: true, tipoComanda: 'POSA' })\n        break\n      case 'create_command_collegamento_partenza':\n        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_PARTENZA' })\n        break\n      case 'create_command_collegamento_arrivo':\n        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_ARRIVO' })\n        break\n      case 'create_command_certificazione':\n        setCreaComandaDialog({ open: true, tipoComanda: 'CERTIFICAZIONE' })\n        break\n      case 'add_multiple_to_command':\n        toast({\n          title: \"Aggiungi Tutti a Comanda\",\n          description: \"Funzione aggiunta multipla a comanda in sviluppo\",\n        })\n        break\n      case 'remove_multiple_from_commands':\n        toast({\n          title: \"Rimuovi Tutti dalle Comande\",\n          description: \"Funzione rimozione multipla dalle comande in sviluppo\",\n        })\n        break\n      default:\n        toast({\n          title: \"Azione non implementata\",\n          description: `Azione ${action} non ancora implementata`,\n        })\n        break\n    }\n  }\n\n  // Gestione successo/errore dialoghi\n  const handleDialogSuccess = (message: string) => {\n    toast({\n      title: \"Operazione completata\",\n      description: message,\n    })\n    // Ricarica i dati\n    loadCavi()\n  }\n\n  const handleDialogError = (message: string) => {\n    toast({\n      title: \"Errore\",\n      description: message,\n      variant: \"destructive\"\n    })\n  }\n\n  // Handler unificato per la nuova modale\n  const handleUnifiedModalSave = async (data: any) => {\n    try {\n      console.log('🔍 DEBUG handleUnifiedModalSave:', {\n        cantiere,\n        cantiereId,\n        cantiereForDialog,\n        isValidCantiere,\n        data\n      })\n\n      if (!cantiereId || !isValidCantiere) {\n        throw new Error('Cantiere non selezionato o non valido')\n      }\n\n      let message = ''\n\n      if (data.mode === 'aggiungi_metri') {\n        // Gestione inserimento metri\n        console.log('🚀 UnifiedModal: Inserimento metri:', data)\n\n        await caviApi.updateMetriPosati(\n          cantiereId,\n          data.cableId,\n          data.metersToInstall,\n          data.bobbinId === 'BOBINA_VUOTA' ? 'BOBINA_VUOTA' : data.bobbinId,\n          true\n        )\n\n        message = `Metri posati aggiornati con successo per il cavo ${data.cableId}`\n\n      } else if (data.mode === 'modifica_bobina') {\n        // Gestione modifica bobina\n        console.log('🚀 UnifiedModal: Modifica bobina:', data)\n\n        if (data.editOption === 'cambia_bobina') {\n          await caviApi.updateMetriPosati(\n            cantiereId,\n            data.cableId,\n            data.newLaidMeters,\n            data.newBobbinId,\n            true\n          )\n\n          message = `Bobina aggiornata con successo per il cavo ${data.cableId}`\n\n        } else if (data.editOption === 'bobina_vuota') {\n          await caviApi.updateMetriPosati(\n            cantiereId,\n            data.cableId,\n            data.newLaidMeters,\n            'BOBINA_VUOTA',\n            false\n          )\n\n          message = `Bobina rimossa con successo per il cavo ${data.cableId}`\n\n        } else if (data.editOption === 'annulla_posa') {\n          // Implementa logica per annullare la posa\n          await caviApi.updateMetriPosati(\n            cantiereId,\n            data.cableId,\n            0,\n            'BOBINA_VUOTA',\n            false\n          )\n\n          message = `Posa annullata con successo per il cavo ${data.cableId}`\n        }\n      }\n\n      // Ricarica i dati\n      await loadCavi()\n\n      // Mostra messaggio di successo\n      toast({\n        title: \"Operazione completata\",\n        description: message,\n        variant: \"default\"\n      })\n\n    } catch (error: any) {\n      console.error('Errore unified modal save:', error)\n      toast({\n        title: \"Errore\",\n        description: error.message || 'Errore durante l\\'operazione',\n        variant: \"destructive\"\n      })\n      throw error // Re-throw per permettere alla modale di gestire l'errore\n    }\n  }\n\n\n\n  // Gestione dei nuovi modali migliorati\n  const handleDisconnectSuccess = () => {\n    setSuccessToast({ visible: true, message: 'Cavo scollegato con successo' })\n    setDisconnectModal({ open: false, cavo: null })\n    loadCavi() // Ricarica i dati\n  }\n\n  const handleDisconnectError = (error: string) => {\n    setCertificationErrorModal({\n      open: true,\n      cavo: disconnectModal.cavo,\n      error\n    })\n    setDisconnectModal({ open: false, cavo: null })\n  }\n\n  const handlePdfSuccess = () => {\n    setSuccessToast({ visible: true, message: 'PDF generato con successo' })\n    setGeneratePdfModal({ open: false, cavo: null })\n  }\n\n  const handlePdfError = (error: string) => {\n    setCertificationErrorModal({\n      open: true,\n      cavo: generatePdfModal.cavo,\n      error\n    })\n    setGeneratePdfModal({ open: false, cavo: null })\n  }\n\n  const handleCertificationSuccess = () => {\n    setSuccessToast({ visible: true, message: 'Certificazione completata con successo' })\n    setCertificationModal({ open: false, cavo: null })\n    loadCavi() // Ricarica i dati\n  }\n\n  const handleCertificationError = (error: string) => {\n    setCertificationErrorModal({\n      open: true,\n      cavo: certificationModal.cavo,\n      error\n    })\n    setCertificationModal({ open: false, cavo: null })\n  }\n\n  // Mostra loader se stiamo caricando i dati dei cavi\n  if (loading && isValidCantiere) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n        <span className=\"ml-2\">Caricamento cavi...</span>\n      </div>\n    )\n  }\n\n  return (\n    <CantiereErrorBoundary>\n      <div className=\"max-w-[90%] mx-auto p-6\">\n\n        {/* Mostra errore specifico dei cavi se presente */}\n        {error && (\n          <Alert variant=\"destructive\" className=\"mb-6\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription>{error}</AlertDescription>\n          </Alert>\n        )}\n\n        {/* Statistics */}\n        <CaviStatistics\n        cavi={cavi}\n        filteredCavi={filteredCavi}\n        revisioneCorrente={revisioneCorrente}\n        className=\"mb-2\"\n      />\n\n      {/* Tabella Cavi Attivi */}\n      <div className=\"mb-8\">\n        <CaviTable\n          cavi={cavi}\n          loading={loading}\n          selectionEnabled={selectionEnabled}\n          selectedCavi={selectedCavi}\n          onSelectionChange={setSelectedCavi}\n          onStatusAction={handleStatusAction}\n          onContextMenuAction={handleContextMenuAction}\n        />\n      </div>\n\n      {/* Tabella Cavi Spare */}\n      {caviSpare.length > 0 && (\n        <div className=\"mb-8\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Package className=\"h-5 w-5\" />\n                <span>Cavi Spare ({caviSpare.length})</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CaviTable\n                cavi={caviSpare}\n                loading={loading}\n                selectionEnabled={false}\n                onStatusAction={handleStatusAction}\n                onContextMenuAction={handleContextMenuAction}\n              />\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n\n\n      {/* Nuovi Modali Migliorati */}\n\n      {/* Modale Unificata per Gestione Cavi e Bobine */}\n      <UnifiedCableBobbinModal\n        mode={unifiedModal.mode || 'aggiungi_metri'}\n        open={unifiedModal.open}\n        onClose={() => setUnifiedModal({ open: false, mode: null, cavo: null })}\n        cavo={unifiedModal.cavo}\n        cantiere={cantiereForDialog}\n        onSave={handleUnifiedModalSave}\n      />\n\n      {/* Modali Legacy (mantenute per compatibilità temporanea) */}\n      <InserisciMetriDialogOld\n        open={inserisciMetriDialog.open}\n        onClose={() => setInserisciMetriDialog({ open: false, cavo: null })}\n        cavo={inserisciMetriDialog.cavo}\n        cantiere={cantiereForDialog}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n\n\n      {collegamentiDialog.cavo && (\n        <CollegamentiDialogSimple\n          open={collegamentiDialog.open}\n          onClose={() => setCollegamentiDialog({ open: false, cavo: null })}\n          cavo={collegamentiDialog.cavo}\n          cantiere={cantiereForDialog}\n          onSuccess={() => {\n            toast({\n              title: \"Successo\",\n              description: \"Operazione completata con successo\",\n            })\n            loadCavi() // Ricarica i dati\n          }}\n        />\n      )}\n\n      <DisconnectCableModal\n        open={disconnectModal.open}\n        onClose={() => setDisconnectModal({ open: false, cavo: null })}\n        cavo={disconnectModal.cavo}\n        onConfirm={handleDisconnectSuccess}\n        onError={handleDisconnectError}\n      />\n\n      <GeneratePdfModal\n        open={generatePdfModal.open}\n        onClose={() => setGeneratePdfModal({ open: false, cavo: null })}\n        cavo={generatePdfModal.cavo}\n        onSuccess={handlePdfSuccess}\n        onError={handlePdfError}\n      />\n\n      <CertificationModal\n        open={certificationModal.open}\n        onClose={() => setCertificationModal({ open: false, cavo: null })}\n        cavo={certificationModal.cavo}\n        onSuccess={handleCertificationSuccess}\n        onError={handleCertificationError}\n      />\n\n      <CertificationErrorModal\n        open={certificationErrorModal.open}\n        onClose={() => setCertificationErrorModal({ open: false, cavo: null, error: '' })}\n        cavo={certificationErrorModal.cavo}\n        error={certificationErrorModal.error}\n        onRetry={() => {\n          setCertificationErrorModal({ open: false, cavo: null, error: '' })\n          // Riapri il modal appropriato basato sul contesto\n          if (certificationErrorModal.cavo) {\n            setCertificationModal({ open: true, cavo: certificationErrorModal.cavo })\n          }\n        }}\n      />\n\n      <SuccessToast\n        visible={successToast.visible}\n        message={successToast.message}\n        onClose={() => setSuccessToast({ visible: false, message: '' })}\n      />\n\n      <CreaComandaDialog\n        open={creaComandaDialog.open}\n        onClose={() => setCreaComandaDialog({ open: false })}\n        caviSelezionati={selectedCavi}\n        tipoComanda={creaComandaDialog.tipoComanda}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <ImportExcelDialog\n        open={importExcelDialog.open}\n        onClose={() => setImportExcelDialog({ open: false })}\n        tipo={importExcelDialog.tipo || 'cavi'}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <ExportDataDialog\n        open={exportDataDialog}\n        onClose={() => setExportDataDialog(false)}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      {/* Dialog per gestione cavi */}\n      <AggiungiCavoDialog\n        open={aggiungiCavoDialog}\n        onClose={() => setAggiungiCavoDialog(false)}\n        cantiere={cantiere}\n        onSuccess={(message) => {\n          handleDialogSuccess(message)\n          loadCavi() // Ricarica la lista cavi\n        }}\n        onError={handleDialogError}\n      />\n\n      <ModificaCavoDialog\n        open={modificaCavoDialog.open}\n        onClose={() => setModificaCavoDialog({ open: false, cavo: null })}\n        cavo={modificaCavoDialog.cavo}\n        cantiere={cantiere}\n        onSuccess={(message) => {\n          handleDialogSuccess(message)\n          loadCavi() // Ricarica la lista cavi\n        }}\n        onError={handleDialogError}\n      />\n\n      <EliminaCavoDialog\n        open={eliminaCavoDialog.open}\n        onClose={() => setEliminaCavoDialog({ open: false, cavo: null })}\n        cavo={eliminaCavoDialog.cavo}\n        cantiere={cantiere}\n        onSuccess={(message) => {\n          handleDialogSuccess(message)\n          loadCavi() // Ricarica la lista cavi\n        }}\n        onError={handleDialogError}\n      />\n      </div>\n    </CantiereErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA,gDAAgD;AAChD;AAOA;AAKA,4DAA4D;AAC5D;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AAAA;AAAA;;;AAvCA;;;;;;;;;;;;;;;;;;;;;;;AA2De,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,eAAe,EAAE,OAAO,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC9G,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAIvB,yBAAyB;IACzB,MAAM,QAAQ,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAA4D;IACtG,2CAA2C;IAC7C;IACA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnE,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,gBAAgB;QAClB;6BAAG;QAAC;KAAK;IAET,uBAAuB;IACvB,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5D;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,sCAAsC;IACtC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGxD;QAAE,MAAM;QAAO,MAAM;IAAK;IAC7B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGtD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5D;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,gCAAgC;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAI5C;QAAE,MAAM;QAAO,MAAM;QAAM,MAAM;IAAK;IAEzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGxD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,sCAAsC;IACtC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGlD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGpD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGxD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAIlE;QAAE,MAAM;QAAO,MAAM;QAAM,OAAO;IAAG;IAExC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5C;QAAE,SAAS;QAAO,SAAS;IAAG;IAEjC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGtD;QAAE,MAAM;IAAM;IAEjB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGtD;QAAE,MAAM;IAAM;IAEjB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,aAAa;QACb,0BAA0B;QAC1B,yBAAyB;QACzB,2BAA2B;QAC3B,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,kBAAkB;IACpB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;gBACpC,OAAO,IAAI,CAAC;YACd;QACF;6BAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,sCAAsC;IACtC,MAAM,oBAAoB,YAAY,CAAC,cAAc,aAAa,IAAI;QACpE,aAAa;QACb,UAAU,CAAC,SAAS,EAAE,YAAY;IACpC,IAAI,IAAI;IAER,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,mBAAmB,cAAc,aAAa,KAAK,CAAC,iBAAiB;gBAEvE;gBACA;YACF,OAAO,IAAI,CAAC,mBAAmB,CAAC,iBAAiB;gBAE/C,QAAQ,EAAE;gBACV,aAAa,EAAE;gBACf,SAAS,iBAAiB;YAC5B;QACF;6BAAG;QAAC;QAAY;QAAiB;QAAiB;KAAc;IAEhE,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,WAAW,mBAAmB,CAAC,EAAE;gBAC9F,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;oBAC1D,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,qBAAqB,KAAK,kBAAkB,IAAI;YAClD,OAAO;gBACL,qBAAqB;YACvB;QACF,EAAE,OAAO,OAAO;YACd,qBAAqB;QACvB;IACF;IAEA,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YACX,SAAS;YAET,gCAAgC;YAChC,IAAI;gBACF,MAAM,OAAO,MAAM,oHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAEnC,6BAA6B;gBAC7B,MAAM,aAAa,KAAK,MAAM,CAAC,CAAC,OAAe,CAAC,KAAK,KAAK;gBAC1D,MAAM,oBAAoB,KAAK,MAAM,CAAC,CAAC,OAAe,KAAK,KAAK;gBAEhE,QAAQ;gBACR,aAAa;gBAEb,sBAAsB;gBACtB,eAAe;YAEjB,EAAE,OAAO,UAAe;gBACtB,MAAM;YACR;QAEF,EAAE,OAAO,OAAY;YACnB,SAAS,CAAC,iCAAiC,EAAE,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,EAAE;QAC9F,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS,SAAS,MAAM;QAC9B,MAAM,aAAa,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,YAAY,IAAI,EAAE,eAAe,IAAI,CAAC,IAAI,GAAG,MAAM;QAC9F,MAAM,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,YAAY,IAAI,EAAE,YAAY,MAAM,GAAG,MAAM,CAAC,gBAAgB;;QACxG,MAAM,cAAc,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;QAE9D,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG;QAC9E,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QACjF,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,GAAG,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QAClH,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QAE7G,SAAS;YACP;YACA;YACA;YACA;YACA,0BAA0B,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,aAAa,SAAU,OAAO;YACjF,yBAAyB,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,SAAU,OAAO;YAC/E,2BAA2B,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,cAAc,SAAU,OAAO;YACnF;YACA;YACA;YACA;QACF;IACF;IAEA,yDAAyD;IACzD,MAAM,qBAAqB,CAAC,MAAY,QAAgB;QAEtD,OAAQ;YACN,KAAK;gBACH,qDAAqD;gBACrD,gBAAgB;oBAAE,MAAM;oBAAM,MAAM;oBAAkB;gBAAK;gBAC3D;YACF,KAAK;gBACH,sDAAsD;gBACtD,gBAAgB;oBAAE,MAAM;oBAAM,MAAM;oBAAmB;gBAAK;gBAC5D;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,iBAAiB,EAAE,MAAM,UAAU,EAAE,KAAK,OAAO,EAAE;gBACnE;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,mDAAmD;gBACnD,sBAAsB;oBAAE,MAAM;oBAAM;gBAAK;gBACzC;YACF,KAAK;gBACH,mEAAmE;gBACnE,sBAAsB;oBAAE,MAAM;oBAAM;gBAAK;gBACzC;YACF,KAAK;gBACH,uCAAuC;gBACvC,sBAAsB;oBAAE,MAAM;oBAAM;gBAAK;gBACzC;YACF,KAAK;gBACH,wCAAwC;gBACxC,oBAAoB;oBAAE,MAAM;oBAAM;gBAAK;gBACvC;QACJ;IACF;IAEA,MAAM,0BAA0B,CAAC,MAAY;QAE3C,OAAQ;YACN,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,2BAA2B,EAAE,KAAK,OAAO,EAAE;gBAC3D;gBACA;YACF,KAAK;gBACH,sBAAsB;oBAAE,MAAM;oBAAM;gBAAK;gBACzC;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM;gBAAK;gBACxC;YACF,KAAK;gBACH,sBAAsB;gBACtB;YACF,KAAK;gBACH,MAAM,aAAa,aAAa,QAAQ,CAAC,KAAK,OAAO;gBACrD,IAAI,YAAY;oBACd,gBAAgB,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO,KAAK,OAAO;oBAC7D,MAAM;wBACJ,OAAO;wBACP,aAAa,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,cAAc,CAAC;oBACnD;gBACF,OAAO;oBACL,gBAAgB;2BAAI;wBAAc,KAAK,OAAO;qBAAC;oBAC/C,MAAM;wBACJ,OAAO;wBACP,aAAa,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,YAAY,CAAC;oBACjD;gBACF;gBACA;YACF,KAAK;gBACH,UAAU,SAAS,CAAC,SAAS,CAAC,KAAK,OAAO;gBAC1C,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,sBAAsB,CAAC;gBAC9D;gBACA;YACF,KAAK;gBACH,MAAM,UAAU,CAAC,IAAI,EAAE,KAAK,OAAO,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,cAAc,EAAE,KAAK,UAAU,IAAI,KAAK,OAAO,CAAC,SAAS,EAAE,KAAK,aAAa,EAAE;gBACjJ,UAAU,SAAS,CAAC,SAAS,CAAC;gBAC9B,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAO;gBACvD;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAwB;gBACxE;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAsB;gBACtE;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAiB;gBACjE;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF;gBACE,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,OAAO,EAAE,OAAO,wBAAwB,CAAC;gBACzD;gBACA;QACJ;IACF;IAEA,oCAAoC;IACpC,MAAM,sBAAsB,CAAC;QAC3B,MAAM;YACJ,OAAO;YACP,aAAa;QACf;QACA,kBAAkB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM;YACJ,OAAO;YACP,aAAa;YACb,SAAS;QACX;IACF;IAEA,wCAAwC;IACxC,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,QAAQ,GAAG,CAAC,oCAAoC;gBAC9C;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,IAAI,CAAC,cAAc,CAAC,iBAAiB;gBACnC,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,UAAU;YAEd,IAAI,KAAK,IAAI,KAAK,kBAAkB;gBAClC,6BAA6B;gBAC7B,QAAQ,GAAG,CAAC,uCAAuC;gBAEnD,MAAM,oHAAA,CAAA,UAAO,CAAC,iBAAiB,CAC7B,YACA,KAAK,OAAO,EACZ,KAAK,eAAe,EACpB,KAAK,QAAQ,KAAK,iBAAiB,iBAAiB,KAAK,QAAQ,EACjE;gBAGF,UAAU,CAAC,iDAAiD,EAAE,KAAK,OAAO,EAAE;YAE9E,OAAO,IAAI,KAAK,IAAI,KAAK,mBAAmB;gBAC1C,2BAA2B;gBAC3B,QAAQ,GAAG,CAAC,qCAAqC;gBAEjD,IAAI,KAAK,UAAU,KAAK,iBAAiB;oBACvC,MAAM,oHAAA,CAAA,UAAO,CAAC,iBAAiB,CAC7B,YACA,KAAK,OAAO,EACZ,KAAK,aAAa,EAClB,KAAK,WAAW,EAChB;oBAGF,UAAU,CAAC,2CAA2C,EAAE,KAAK,OAAO,EAAE;gBAExE,OAAO,IAAI,KAAK,UAAU,KAAK,gBAAgB;oBAC7C,MAAM,oHAAA,CAAA,UAAO,CAAC,iBAAiB,CAC7B,YACA,KAAK,OAAO,EACZ,KAAK,aAAa,EAClB,gBACA;oBAGF,UAAU,CAAC,wCAAwC,EAAE,KAAK,OAAO,EAAE;gBAErE,OAAO,IAAI,KAAK,UAAU,KAAK,gBAAgB;oBAC7C,0CAA0C;oBAC1C,MAAM,oHAAA,CAAA,UAAO,CAAC,iBAAiB,CAC7B,YACA,KAAK,OAAO,EACZ,GACA,gBACA;oBAGF,UAAU,CAAC,wCAAwC,EAAE,KAAK,OAAO,EAAE;gBACrE;YACF;YAEA,kBAAkB;YAClB,MAAM;YAEN,+BAA+B;YAC/B,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QAEF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO,IAAI;gBAC9B,SAAS;YACX;YACA,MAAM,MAAM,0DAA0D;;QACxE;IACF;IAIA,uCAAuC;IACvC,MAAM,0BAA0B;QAC9B,gBAAgB;YAAE,SAAS;YAAM,SAAS;QAA+B;QACzE,mBAAmB;YAAE,MAAM;YAAO,MAAM;QAAK;QAC7C,WAAW,kBAAkB;;IAC/B;IAEA,MAAM,wBAAwB,CAAC;QAC7B,2BAA2B;YACzB,MAAM;YACN,MAAM,gBAAgB,IAAI;YAC1B;QACF;QACA,mBAAmB;YAAE,MAAM;YAAO,MAAM;QAAK;IAC/C;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;YAAE,SAAS;YAAM,SAAS;QAA4B;QACtE,oBAAoB;YAAE,MAAM;YAAO,MAAM;QAAK;IAChD;IAEA,MAAM,iBAAiB,CAAC;QACtB,2BAA2B;YACzB,MAAM;YACN,MAAM,iBAAiB,IAAI;YAC3B;QACF;QACA,oBAAoB;YAAE,MAAM;YAAO,MAAM;QAAK;IAChD;IAEA,MAAM,6BAA6B;QACjC,gBAAgB;YAAE,SAAS;YAAM,SAAS;QAAyC;QACnF,sBAAsB;YAAE,MAAM;YAAO,MAAM;QAAK;QAChD,WAAW,kBAAkB;;IAC/B;IAEA,MAAM,2BAA2B,CAAC;QAChC,2BAA2B;YACzB,MAAM;YACN,MAAM,mBAAmB,IAAI;YAC7B;QACF;QACA,sBAAsB;YAAE,MAAM;YAAO,MAAM;QAAK;IAClD;IAEA,oDAAoD;IACpD,IAAI,WAAW,iBAAiB;QAC9B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,6LAAC;oBAAK,WAAU;8BAAO;;;;;;;;;;;;IAG7B;IAEA,qBACE,6LAAC,0JAAA,CAAA,wBAAqB;kBACpB,cAAA,6LAAC;YAAI,WAAU;;gBAGZ,uBACC,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAc,WAAU;;sCACrC,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC,oIAAA,CAAA,mBAAgB;sCAAE;;;;;;;;;;;;8BAKvB,6LAAC,+IAAA,CAAA,UAAc;oBACf,MAAM;oBACN,cAAc;oBACd,mBAAmB;oBACnB,WAAU;;;;;;8BAIZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,0IAAA,CAAA,UAAS;wBACR,MAAM;wBACN,SAAS;wBACT,kBAAkB;wBAClB,cAAc;wBACd,mBAAmB;wBACnB,gBAAgB;wBAChB,qBAAqB;;;;;;;;;;;gBAKxB,UAAU,MAAM,GAAG,mBAClB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;;gDAAK;gDAAa,UAAU,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAGxC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,0IAAA,CAAA,UAAS;oCACR,MAAM;oCACN,SAAS;oCACT,kBAAkB;oCAClB,gBAAgB;oCAChB,qBAAqB;;;;;;;;;;;;;;;;;;;;;;8BAY/B,6LAAC,iKAAA,CAAA,0BAAuB;oBACtB,MAAM,aAAa,IAAI,IAAI;oBAC3B,MAAM,aAAa,IAAI;oBACvB,SAAS,IAAM,gBAAgB;4BAAE,MAAM;4BAAO,MAAM;4BAAM,MAAM;wBAAK;oBACrE,MAAM,aAAa,IAAI;oBACvB,UAAU;oBACV,QAAQ;;;;;;8BAIV,6LAAC,qJAAA,CAAA,UAAuB;oBACtB,MAAM,qBAAqB,IAAI;oBAC/B,SAAS,IAAM,wBAAwB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBACjE,MAAM,qBAAqB,IAAI;oBAC/B,UAAU;oBACV,WAAW;oBACX,SAAS;;;;;;gBAKV,mBAAmB,IAAI,kBACtB,6LAAC,yJAAA,CAAA,UAAwB;oBACvB,MAAM,mBAAmB,IAAI;oBAC7B,SAAS,IAAM,sBAAsB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBAC/D,MAAM,mBAAmB,IAAI;oBAC7B,UAAU;oBACV,WAAW;wBACT,MAAM;4BACJ,OAAO;4BACP,aAAa;wBACf;wBACA,WAAW,kBAAkB;;oBAC/B;;;;;;8BAIJ,6LAAC,4JAAA,CAAA,uBAAoB;oBACnB,MAAM,gBAAgB,IAAI;oBAC1B,SAAS,IAAM,mBAAmB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBAC5D,MAAM,gBAAgB,IAAI;oBAC1B,WAAW;oBACX,SAAS;;;;;;8BAGX,6LAAC,4JAAA,CAAA,mBAAgB;oBACf,MAAM,iBAAiB,IAAI;oBAC3B,SAAS,IAAM,oBAAoB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBAC7D,MAAM,iBAAiB,IAAI;oBAC3B,WAAW;oBACX,SAAS;;;;;;8BAGX,6LAAC,4JAAA,CAAA,qBAAkB;oBACjB,MAAM,mBAAmB,IAAI;oBAC7B,SAAS,IAAM,sBAAsB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBAC/D,MAAM,mBAAmB,IAAI;oBAC7B,WAAW;oBACX,SAAS;;;;;;8BAGX,6LAAC,4JAAA,CAAA,0BAAuB;oBACtB,MAAM,wBAAwB,IAAI;oBAClC,SAAS,IAAM,2BAA2B;4BAAE,MAAM;4BAAO,MAAM;4BAAM,OAAO;wBAAG;oBAC/E,MAAM,wBAAwB,IAAI;oBAClC,OAAO,wBAAwB,KAAK;oBACpC,SAAS;wBACP,2BAA2B;4BAAE,MAAM;4BAAO,MAAM;4BAAM,OAAO;wBAAG;wBAChE,kDAAkD;wBAClD,IAAI,wBAAwB,IAAI,EAAE;4BAChC,sBAAsB;gCAAE,MAAM;gCAAM,MAAM,wBAAwB,IAAI;4BAAC;wBACzE;oBACF;;;;;;8BAGF,6LAAC,4JAAA,CAAA,eAAY;oBACX,SAAS,aAAa,OAAO;oBAC7B,SAAS,aAAa,OAAO;oBAC7B,SAAS,IAAM,gBAAgB;4BAAE,SAAS;4BAAO,SAAS;wBAAG;;;;;;8BAG/D,6LAAC,kJAAA,CAAA,UAAiB;oBAChB,MAAM,kBAAkB,IAAI;oBAC5B,SAAS,IAAM,qBAAqB;4BAAE,MAAM;wBAAM;oBAClD,iBAAiB;oBACjB,aAAa,kBAAkB,WAAW;oBAC1C,WAAW;oBACX,SAAS;;;;;;8BAGX,6LAAC,kJAAA,CAAA,UAAiB;oBAChB,MAAM,kBAAkB,IAAI;oBAC5B,SAAS,IAAM,qBAAqB;4BAAE,MAAM;wBAAM;oBAClD,MAAM,kBAAkB,IAAI,IAAI;oBAChC,WAAW;oBACX,SAAS;;;;;;8BAGX,6LAAC,iJAAA,CAAA,UAAgB;oBACf,MAAM;oBACN,SAAS,IAAM,oBAAoB;oBACnC,WAAW;oBACX,SAAS;;;;;;8BAIX,6LAAC,mJAAA,CAAA,UAAkB;oBACjB,MAAM;oBACN,SAAS,IAAM,sBAAsB;oBACrC,UAAU;oBACV,WAAW,CAAC;wBACV,oBAAoB;wBACpB,WAAW,yBAAyB;;oBACtC;oBACA,SAAS;;;;;;8BAGX,6LAAC,mJAAA,CAAA,UAAkB;oBACjB,MAAM,mBAAmB,IAAI;oBAC7B,SAAS,IAAM,sBAAsB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBAC/D,MAAM,mBAAmB,IAAI;oBAC7B,UAAU;oBACV,WAAW,CAAC;wBACV,oBAAoB;wBACpB,WAAW,yBAAyB;;oBACtC;oBACA,SAAS;;;;;;8BAGX,6LAAC,kJAAA,CAAA,UAAiB;oBAChB,MAAM,kBAAkB,IAAI;oBAC5B,SAAS,IAAM,qBAAqB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBAC9D,MAAM,kBAAkB,IAAI;oBAC5B,UAAU;oBACV,WAAW,CAAC;wBACV,oBAAoB;wBACpB,WAAW,yBAAyB;;oBACtC;oBACA,SAAS;;;;;;;;;;;;;;;;;AAKjB;GA9sBwB;;QACoC,kIAAA,CAAA,UAAO;QACmC,8HAAA,CAAA,cAAW;QAChG,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}