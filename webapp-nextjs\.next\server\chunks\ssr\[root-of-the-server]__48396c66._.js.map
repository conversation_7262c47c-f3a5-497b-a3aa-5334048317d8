{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'\n\n// Configurazione base per l'API\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'\n\n// Crea istanza axios con configurazione base\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n})\n\n// Interceptor per aggiungere il token di autenticazione\napiClient.interceptors.request.use(\n  (config) => {\n    // Verifica se siamo nel browser prima di accedere a localStorage\n    if (typeof window !== 'undefined') {\n      const token = localStorage.getItem('token')\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`\n      }\n    }\n    return config\n  },\n  (error) => {\n    return Promise.reject(error)\n  }\n)\n\n// Interceptor per gestire le risposte e gli errori\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response\n  },\n  (error) => {\n    if (error.response?.status === 401 && typeof window !== 'undefined') {\n      // Token scaduto o non valido\n      localStorage.removeItem('token')\n      localStorage.removeItem('access_token')\n      localStorage.removeItem('user_data')\n      localStorage.removeItem('cantiere_data')\n      window.location.href = '/login'\n    }\n    return Promise.reject(error)\n  }\n)\n\n// Tipi per le risposte API\nexport interface ApiResponse<T = any> {\n  data: T\n  message?: string\n  status: number\n}\n\nexport interface PaginatedResponse<T> {\n  items: T[]\n  total: number\n  page: number\n  size: number\n  pages: number\n}\n\n// Funzioni helper per le chiamate API\nexport const api = {\n  // GET request\n  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.get<T>(url, config)\n    return response.data\n  },\n\n  // POST request\n  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.post<T>(url, data, config)\n    return response.data\n  },\n\n  // PUT request\n  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.put<T>(url, data, config)\n    return response.data\n  },\n\n  // PATCH request\n  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.patch<T>(url, data, config)\n    return response.data\n  },\n\n  // DELETE request\n  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.delete<T>(url, config)\n    return response.data\n  },\n}\n\n// Servizi API specifici per CABLYS\nexport const authApi = {\n  // Login utente - usa FormData per OAuth2PasswordRequestForm\n  login: async (credentials: { username: string; password: string }) => {\n    const formData = new FormData()\n    formData.append('username', credentials.username)\n    formData.append('password', credentials.password)\n\n    const response = await apiClient.post('/api/auth/login', formData, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n    })\n    return response.data\n  },\n\n  // Login cantiere - usa JSON per CantiereLogin\n  loginCantiere: (credentials: { codice_cantiere: string; password_cantiere: string }) =>\n    api.post<{ access_token: string; token_type: string; cantiere: any }>('/api/auth/login/cantiere', {\n      codice_univoco: credentials.codice_cantiere,\n      password: credentials.password_cantiere\n    }),\n\n  // Verifica token\n  verifyToken: () =>\n    api.post<{ user: any }>('/api/auth/test-token'),\n\n  // Logout\n  logout: () => {\n    localStorage.removeItem('access_token')\n    localStorage.removeItem('user_data')\n    window.location.href = '/login'\n  }\n}\n\nexport const caviApi = {\n  // Ottieni tutti i cavi\n  getCavi: (cantiereId: number, params?: {\n    tipo_cavo?: number,\n    stato_installazione?: string,\n    tipologia?: string,\n    sort_by?: string,\n    sort_order?: string\n  }) =>\n    api.get<any[]>(`/api/cavi/${cantiereId}`, { params }),\n\n  // Ottieni cavo specifico\n  getCavo: (cantiereId: number, idCavo: string) =>\n    api.get<any>(`/api/cavi/${cantiereId}/${idCavo}`),\n\n  // Verifica se cavo esiste\n  checkCavo: (cantiereId: number, idCavo: string) =>\n    api.get<any>(`/api/cavi/${cantiereId}/check/${idCavo}`),\n\n  // Crea nuovo cavo\n  createCavo: (cantiereId: number, cavo: any) =>\n    api.post<any>(`/api/cavi/${cantiereId}`, cavo),\n\n  // Aggiorna cavo\n  updateCavo: (cantiereId: number, idCavo: string, updates: any) =>\n    api.put<any>(`/api/cavi/${cantiereId}/${idCavo}`, updates),\n\n  // Elimina cavo\n  deleteCavo: (cantiereId: number, idCavo: string, options?: any) =>\n    api.delete(`/api/cavi/${cantiereId}/${idCavo}`, { data: options }),\n\n  // Aggiorna metri posati\n  updateMetriPosati: (cantiereId: number, idCavo: string, metri: number, idBobina?: string, forceOver?: boolean) =>\n    api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/metri-posati`, {\n      metri_posati: metri,\n      id_bobina: idBobina,\n      force_over: forceOver || false\n    }),\n\n  // Aggiorna bobina\n  updateBobina: (cantiereId: number, idCavo: string, idBobina: string, forceOver?: boolean) =>\n    api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/bobina`, {\n      id_bobina: idBobina,\n      force_over: forceOver || false\n    }),\n\n  // Annulla installazione (resetta completamente il cavo)\n  cancelInstallation: (cantiereId: number, idCavo: string) =>\n    api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/cancel-installation`),\n\n  // Gestione collegamenti\n  collegaCavo: (cantiereId: number, idCavo: string, lato: 'partenza' | 'arrivo', responsabile?: string) =>\n    api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/collegamento`, {\n      lato,\n      responsabile\n    }),\n\n  // Scollega cavo\n  scollegaCavo: (cantiereId: number, idCavo: string, lato?: 'partenza' | 'arrivo') => {\n    const config: any = {}\n    if (lato) {\n      config.data = { lato }\n    }\n    return api.delete(`/api/cavi/${cantiereId}/${idCavo}/collegamento`, config)\n  },\n\n  // Marca come spare\n  markAsSpare: (cantiereId: number, idCavo: string, spare: boolean) =>\n    spare\n      ? api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/mark-as-spare`, {})\n      : api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/reactivate-spare`, {}),\n\n  // Debug endpoints\n  debugCavi: (cantiereId: number) =>\n    api.get<any>(`/api/cavi/debug/${cantiereId}`),\n\n  debugCaviRaw: (cantiereId: number) =>\n    api.get<any>(`/api/cavi/debug/raw/${cantiereId}`),\n}\n\nexport const parcoCaviApi = {\n  // Ottieni tutte le bobine\n  getBobine: (cantiereId: number, params?: {\n    filtro?: string,\n    tipologia?: string,\n    sezione?: string,    // sezione nel DB = formazione sistema\n    disponibili_only?: boolean\n  }) =>\n    api.get<any[]>(`/api/parco-cavi/${cantiereId}`, { params }),\n\n  // Ottieni bobina specifica\n  getBobina: (cantiereId: number, idBobina: string) =>\n    api.get<any>(`/api/parco-cavi/${cantiereId}/${idBobina}`),\n\n  // Ottieni bobine compatibili\n  getBobineCompatibili: (cantiereId: number, params: {\n    tipologia?: string,\n    n_conduttori?: string,\n    sezione?: string     // sezione nel DB = formazione sistema\n  }) =>\n    api.get<any[]>(`/api/parco-cavi/${cantiereId}/compatibili`, { params }),\n\n  // Crea nuova bobina\n  createBobina: (cantiereId: number, bobina: any) =>\n    api.post<any>(`/api/parco-cavi/${cantiereId}`, bobina),\n\n  // Aggiorna bobina esistente\n  updateBobina: (cantiereId: number, bobinaNumero: string, bobina: any) =>\n    api.put<any>(`/api/parco-cavi/${cantiereId}/${bobinaNumero}`, bobina),\n\n  // Elimina bobina\n  deleteBobina: (cantiereId: number, bobinaNumero: string) =>\n    api.delete<any>(`/api/parco-cavi/${cantiereId}/${bobinaNumero}`),\n\n  // Verifica se è il primo inserimento bobina\n  isFirstBobinaInsertion: (cantiereId: number) =>\n    api.get<{is_first_insertion: boolean, configurazione: string}>(`/api/parco-cavi/${cantiereId}/is-first-insertion`),\n\n  // Aggiorna bobina\n  updateBobina: (cantiereId: number, idBobina: string, updates: any) =>\n    api.put<any>(`/api/parco-cavi/${cantiereId}/${idBobina}`, updates),\n\n  // Elimina bobina\n  deleteBobina: (cantiereId: number, idBobina: string) =>\n    api.delete(`/api/parco-cavi/${cantiereId}/${idBobina}`),\n\n  // Verifica disponibilità metri\n  checkDisponibilita: (cantiereId: number, idBobina: string, metriRichiesti: number) =>\n    api.get<any>(`/api/parco-cavi/${cantiereId}/${idBobina}/disponibilita`, {\n      params: { metri_richiesti: metriRichiesti }\n    }),\n}\n\nexport const comandeApi = {\n  // Ottieni tutte le comande\n  getComande: (cantiereId: number) =>\n    api.get<any[]>(`/api/comande/cantiere/${cantiereId}`),\n\n  // Ottieni comanda specifica\n  getComanda: (cantiereId: number, codiceComanda: string) =>\n    api.get<any>(`/api/comande/${codiceComanda}`),\n\n  // Ottieni cavi di una comanda\n  getCaviComanda: (codiceComanda: string) =>\n    api.get<any>(`/api/comande/${codiceComanda}/cavi`),\n\n  // Crea nuova comanda\n  createComanda: (cantiereId: number, comanda: any) =>\n    api.post<any>(`/api/comande/cantiere/${cantiereId}`, comanda),\n\n  // Crea comanda con cavi\n  createComandaWithCavi: (cantiereId: number, comanda: any, caviIds: string[]) =>\n    api.post<any>(`/api/comande/cantiere/${cantiereId}/crea-con-cavi`, comanda, {\n      params: { lista_id_cavi: caviIds }\n    }),\n\n  // Aggiorna dati comanda (posa, collegamento, certificazione)\n  updateDatiComanda: (codiceComanda: string, endpoint: string, data: any) =>\n    api.put<any>(`/api/comande/${codiceComanda}/${endpoint}`, data),\n\n  // Aggiorna comanda\n  updateComanda: (cantiereId: number, codiceComanda: string, updates: any) =>\n    api.put<any>(`/api/comande/cantiere/${cantiereId}/${codiceComanda}`, updates),\n\n  // Elimina comanda\n  deleteComanda: (cantiereId: number, codiceComanda: string) =>\n    api.delete(`/api/comande/cantiere/${cantiereId}/${codiceComanda}`),\n\n  // Assegna cavi a comanda\n  assegnaCavi: (cantiereId: number, codiceComanda: string, caviIds: string[]) =>\n    api.post<any>(`/api/comande/cantiere/${cantiereId}/${codiceComanda}/assegna-cavi`, { cavi_ids: caviIds }),\n\n  // Rimuovi cavi da comanda\n  rimuoviCavi: (cantiereId: number, codiceComanda: string, caviIds: string[]) =>\n    api.delete(`/api/comande/cantiere/${cantiereId}/${codiceComanda}/rimuovi-cavi`, {\n      data: { cavi_ids: caviIds }\n    }),\n\n  // Ottieni statistiche comande\n  getStatistiche: (cantiereId: number) =>\n    api.get<any>(`/api/comande/cantiere/${cantiereId}/statistiche`),\n\n  // Cambia stato comanda\n  cambiaStato: (cantiereId: number, codiceComanda: string, nuovoStato: string) =>\n    api.put<any>(`/api/comande/cantiere/${cantiereId}/${codiceComanda}/stato`, {\n      nuovo_stato: nuovoStato\n    }),\n}\n\nexport const responsabiliApi = {\n  // Ottieni tutti i responsabili\n  getResponsabili: (cantiereId: number) =>\n    api.get<any[]>(`/api/responsabili/cantiere/${cantiereId}`),\n\n  // Crea nuovo responsabile\n  createResponsabile: (cantiereId: number, responsabile: any) =>\n    api.post<any>(`/api/responsabili/${cantiereId}`, responsabile),\n\n  // Aggiorna responsabile\n  updateResponsabile: (cantiereId: number, id: number, updates: any) =>\n    api.put<any>(`/api/responsabili/${cantiereId}/${id}`, updates),\n\n  // Elimina responsabile\n  deleteResponsabile: (cantiereId: number, id: number) =>\n    api.delete(`/api/responsabili/${cantiereId}/${id}`),\n}\n\nexport const certificazioniApi = {\n  // Ottieni certificazioni\n  getCertificazioni: (cantiereId: number) =>\n    api.get<any[]>(`/api/cantieri/${cantiereId}/certificazioni`),\n\n  // Crea certificazione\n  createCertificazione: (cantiereId: number, certificazione: any) =>\n    api.post<any>(`/api/cantieri/${cantiereId}/certificazioni`, certificazione),\n\n  // Genera PDF certificato\n  generatePDF: (cantiereId: number, idCavo: string) =>\n    api.get(`/api/cantieri/${cantiereId}/pdf-cei-64-8/${idCavo}`, {\n      responseType: 'blob'\n    }),\n}\n\nexport const excelApi = {\n  // Import cavi da Excel\n  importCavi: (cantiereId: number, file: File, revisione: string) => {\n    const formData = new FormData()\n    formData.append('file', file)\n    formData.append('revisione', revisione)\n    return api.post<any>(`/api/excel/${cantiereId}/import-cavi`, formData, {\n      headers: { 'Content-Type': 'multipart/form-data' }\n    })\n  },\n\n  // Import bobine da Excel\n  importBobine: (cantiereId: number, file: File) => {\n    const formData = new FormData()\n    formData.append('file', file)\n    return api.post<any>(`/api/excel/${cantiereId}/import-parco-bobine`, formData, {\n      headers: { 'Content-Type': 'multipart/form-data' }\n    })\n  },\n\n  // Export cavi\n  exportCavi: (cantiereId: number) =>\n    api.get(`/api/excel/${cantiereId}/export-cavi`, {\n      responseType: 'blob'\n    }),\n\n  // Export bobine\n  exportBobine: (cantiereId: number) =>\n    api.get(`/api/excel/${cantiereId}/export-parco-bobine`, {\n      responseType: 'blob'\n    }),\n}\n\nexport const reportsApi = {\n  // Report avanzamento\n  getReportAvanzamento: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/avanzamento`),\n\n  // Report BOQ\n  getReportBOQ: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/boq`),\n\n  // Report utilizzo bobine (storico bobine)\n  getReportUtilizzoBobine: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/storico-bobine`),\n\n  // Report progress\n  getReportProgress: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/progress`),\n\n  // Report posa per periodo\n  getReportPosaPeriodo: (cantiereId: number, dataInizio?: string, dataFine?: string) => {\n    const params = new URLSearchParams()\n    if (dataInizio) params.append('data_inizio', dataInizio)\n    if (dataFine) params.append('data_fine', dataFine)\n    const queryString = params.toString()\n    return api.get<any>(`/api/reports/${cantiereId}/posa-periodo${queryString ? `?${queryString}` : ''}`)\n  },\n}\n\nexport const cantieriApi = {\n  // Ottieni tutti i cantieri\n  getCantieri: () =>\n    api.get<any[]>('/api/cantieri'),\n\n  // Ottieni cantiere specifico\n  getCantiere: (id: number) =>\n    api.get<any>(`/api/cantieri/${id}`),\n\n  // Crea nuovo cantiere\n  createCantiere: (cantiere: any) =>\n    api.post<any>('/api/cantieri', cantiere),\n\n  // Aggiorna cantiere\n  updateCantiere: (id: number, updates: any) =>\n    api.put<any>(`/api/cantieri/${id}`, updates),\n\n  // Ottieni statistiche cantiere\n  getCantiereStatistics: (id: number) =>\n    api.get<{\n      cantiere_id: number\n      total_cavi: number\n      installati: number\n      collegati: number\n      certificati: number\n      percentuale_avanzamento: number\n      iap: number\n    }>(`/api/cantieri/${id}/statistics`),\n}\n\nexport const usersApi = {\n  // Ottieni tutti gli utenti (solo admin)\n  getUsers: () =>\n    api.get<any[]>('/api/users'),\n\n  // Ottieni utente specifico\n  getUser: (id: number) =>\n    api.get<any>(`/api/users/${id}`),\n\n  // Crea nuovo utente\n  createUser: (user: any) =>\n    api.post<any>('/api/users', user),\n\n  // Aggiorna utente\n  updateUser: (id: number, updates: any) =>\n    api.put<any>(`/api/users/${id}`, updates),\n\n  // Elimina utente\n  deleteUser: (id: number) =>\n    api.delete(`/api/users/${id}`),\n\n  // Abilita/Disabilita utente\n  toggleUserStatus: (id: number) =>\n    api.get<any>(`/api/users/toggle/${id}`),\n\n  // Verifica utenti scaduti\n  checkExpiredUsers: () =>\n    api.get<any>('/api/users/check-expired'),\n\n  // Impersona utente\n  impersonateUser: (userId: number) =>\n    api.post<any>('/api/auth/impersonate', { user_id: userId }),\n\n  // Ottieni dati database raw\n  getDatabaseData: () =>\n    api.get<any>('/api/users/db-raw'),\n\n  // Reset database\n  resetDatabase: () =>\n    api.post<any>('/api/admin/reset-database'),\n}\n\nexport default apiClient\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAEA,gCAAgC;AAChC,MAAM,eAAe,6DAAmC;AAExD,6CAA6C;AAC7C,MAAM,YAA2B,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,wDAAwD;AACxD,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,iEAAiE;IACjE,uCAAmC;;IAKnC;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,mDAAmD;AACnD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,gBAAkB,aAAa;;IAOrE;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAmBK,MAAM,MAAM;IACjB,cAAc;IACd,KAAK,OAAgB,KAAa;QAChC,MAAM,WAAW,MAAM,UAAU,GAAG,CAAI,KAAK;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,MAAM,OAAgB,KAAa,MAAY;QAC7C,MAAM,WAAW,MAAM,UAAU,IAAI,CAAI,KAAK,MAAM;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,KAAK,OAAgB,KAAa,MAAY;QAC5C,MAAM,WAAW,MAAM,UAAU,GAAG,CAAI,KAAK,MAAM;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,OAAO,OAAgB,KAAa,MAAY;QAC9C,MAAM,WAAW,MAAM,UAAU,KAAK,CAAI,KAAK,MAAM;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,QAAQ,OAAgB,KAAa;QACnC,MAAM,WAAW,MAAM,UAAU,MAAM,CAAI,KAAK;QAChD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4DAA4D;IAC5D,OAAO,OAAO;QACZ,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAChD,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAEhD,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,mBAAmB,UAAU;YACjE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,8CAA8C;IAC9C,eAAe,CAAC,cACd,IAAI,IAAI,CAA8D,4BAA4B;YAChG,gBAAgB,YAAY,eAAe;YAC3C,UAAU,YAAY,iBAAiB;QACzC;IAEF,iBAAiB;IACjB,aAAa,IACX,IAAI,IAAI,CAAgB;IAE1B,SAAS;IACT,QAAQ;QACN,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;AACF;AAEO,MAAM,UAAU;IACrB,uBAAuB;IACvB,SAAS,CAAC,YAAoB,SAO5B,IAAI,GAAG,CAAQ,CAAC,UAAU,EAAE,YAAY,EAAE;YAAE;QAAO;IAErD,yBAAyB;IACzB,SAAS,CAAC,YAAoB,SAC5B,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,QAAQ;IAElD,0BAA0B;IAC1B,WAAW,CAAC,YAAoB,SAC9B,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,WAAW,OAAO,EAAE,QAAQ;IAExD,kBAAkB;IAClB,YAAY,CAAC,YAAoB,OAC/B,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,YAAY,EAAE;IAE3C,gBAAgB;IAChB,YAAY,CAAC,YAAoB,QAAgB,UAC/C,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,QAAQ,EAAE;IAEpD,eAAe;IACf,YAAY,CAAC,YAAoB,QAAgB,UAC/C,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,QAAQ,EAAE;YAAE,MAAM;QAAQ;IAElE,wBAAwB;IACxB,mBAAmB,CAAC,YAAoB,QAAgB,OAAe,UAAmB,YACxF,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YAC9D,cAAc;YACd,WAAW;YACX,YAAY,aAAa;QAC3B;IAEF,kBAAkB;IAClB,cAAc,CAAC,YAAoB,QAAgB,UAAkB,YACnE,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE;YACxD,WAAW;YACX,YAAY,aAAa;QAC3B;IAEF,wDAAwD;IACxD,oBAAoB,CAAC,YAAoB,SACvC,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,oBAAoB,CAAC;IAEvE,wBAAwB;IACxB,aAAa,CAAC,YAAoB,QAAgB,MAA6B,eAC7E,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YAC9D;YACA;QACF;IAEF,gBAAgB;IAChB,cAAc,CAAC,YAAoB,QAAgB;QACjD,MAAM,SAAc,CAAC;QACrB,IAAI,MAAM;YACR,OAAO,IAAI,GAAG;gBAAE;YAAK;QACvB;QACA,OAAO,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;IACtE;IAEA,mBAAmB;IACnB,aAAa,CAAC,YAAoB,QAAgB,QAChD,QACI,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,cAAc,CAAC,EAAE,CAAC,KAClE,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,iBAAiB,CAAC,EAAE,CAAC;IAE3E,kBAAkB;IAClB,WAAW,CAAC,aACV,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,YAAY;IAE9C,cAAc,CAAC,aACb,IAAI,GAAG,CAAM,CAAC,oBAAoB,EAAE,YAAY;AACpD;AAEO,MAAM,eAAe;IAC1B,0BAA0B;IAC1B,WAAW,CAAC,YAAoB,SAM9B,IAAI,GAAG,CAAQ,CAAC,gBAAgB,EAAE,YAAY,EAAE;YAAE;QAAO;IAE3D,2BAA2B;IAC3B,WAAW,CAAC,YAAoB,WAC9B,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAU;IAE1D,6BAA6B;IAC7B,sBAAsB,CAAC,YAAoB,SAKzC,IAAI,GAAG,CAAQ,CAAC,gBAAgB,EAAE,WAAW,YAAY,CAAC,EAAE;YAAE;QAAO;IAEvE,oBAAoB;IACpB,cAAc,CAAC,YAAoB,SACjC,IAAI,IAAI,CAAM,CAAC,gBAAgB,EAAE,YAAY,EAAE;IAEjD,4BAA4B;IAC5B,cAAc,CAAC,YAAoB,cAAsB,SACvD,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,cAAc,EAAE;IAEhE,iBAAiB;IACjB,cAAc,CAAC,YAAoB,eACjC,IAAI,MAAM,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,cAAc;IAEjE,4CAA4C;IAC5C,wBAAwB,CAAC,aACvB,IAAI,GAAG,CAAwD,CAAC,gBAAgB,EAAE,WAAW,mBAAmB,CAAC;IAEnH,kBAAkB;IAClB,cAAc,CAAC,YAAoB,UAAkB,UACnD,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAU,EAAE;IAE5D,iBAAiB;IACjB,cAAc,CAAC,YAAoB,WACjC,IAAI,MAAM,CAAC,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAU;IAExD,+BAA+B;IAC/B,oBAAoB,CAAC,YAAoB,UAAkB,iBACzD,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,SAAS,cAAc,CAAC,EAAE;YACtE,QAAQ;gBAAE,iBAAiB;YAAe;QAC5C;AACJ;AAEO,MAAM,aAAa;IACxB,2BAA2B;IAC3B,YAAY,CAAC,aACX,IAAI,GAAG,CAAQ,CAAC,sBAAsB,EAAE,YAAY;IAEtD,4BAA4B;IAC5B,YAAY,CAAC,YAAoB,gBAC/B,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,eAAe;IAE9C,8BAA8B;IAC9B,gBAAgB,CAAC,gBACf,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,cAAc,KAAK,CAAC;IAEnD,qBAAqB;IACrB,eAAe,CAAC,YAAoB,UAClC,IAAI,IAAI,CAAM,CAAC,sBAAsB,EAAE,YAAY,EAAE;IAEvD,wBAAwB;IACxB,uBAAuB,CAAC,YAAoB,SAAc,UACxD,IAAI,IAAI,CAAM,CAAC,sBAAsB,EAAE,WAAW,cAAc,CAAC,EAAE,SAAS;YAC1E,QAAQ;gBAAE,eAAe;YAAQ;QACnC;IAEF,6DAA6D;IAC7D,mBAAmB,CAAC,eAAuB,UAAkB,OAC3D,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,cAAc,CAAC,EAAE,UAAU,EAAE;IAE5D,mBAAmB;IACnB,eAAe,CAAC,YAAoB,eAAuB,UACzD,IAAI,GAAG,CAAM,CAAC,sBAAsB,EAAE,WAAW,CAAC,EAAE,eAAe,EAAE;IAEvE,kBAAkB;IAClB,eAAe,CAAC,YAAoB,gBAClC,IAAI,MAAM,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,EAAE,eAAe;IAEnE,yBAAyB;IACzB,aAAa,CAAC,YAAoB,eAAuB,UACvD,IAAI,IAAI,CAAM,CAAC,sBAAsB,EAAE,WAAW,CAAC,EAAE,cAAc,aAAa,CAAC,EAAE;YAAE,UAAU;QAAQ;IAEzG,0BAA0B;IAC1B,aAAa,CAAC,YAAoB,eAAuB,UACvD,IAAI,MAAM,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,EAAE,cAAc,aAAa,CAAC,EAAE;YAC9E,MAAM;gBAAE,UAAU;YAAQ;QAC5B;IAEF,8BAA8B;IAC9B,gBAAgB,CAAC,aACf,IAAI,GAAG,CAAM,CAAC,sBAAsB,EAAE,WAAW,YAAY,CAAC;IAEhE,uBAAuB;IACvB,aAAa,CAAC,YAAoB,eAAuB,aACvD,IAAI,GAAG,CAAM,CAAC,sBAAsB,EAAE,WAAW,CAAC,EAAE,cAAc,MAAM,CAAC,EAAE;YACzE,aAAa;QACf;AACJ;AAEO,MAAM,kBAAkB;IAC7B,+BAA+B;IAC/B,iBAAiB,CAAC,aAChB,IAAI,GAAG,CAAQ,CAAC,2BAA2B,EAAE,YAAY;IAE3D,0BAA0B;IAC1B,oBAAoB,CAAC,YAAoB,eACvC,IAAI,IAAI,CAAM,CAAC,kBAAkB,EAAE,YAAY,EAAE;IAEnD,wBAAwB;IACxB,oBAAoB,CAAC,YAAoB,IAAY,UACnD,IAAI,GAAG,CAAM,CAAC,kBAAkB,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE;IAExD,uBAAuB;IACvB,oBAAoB,CAAC,YAAoB,KACvC,IAAI,MAAM,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,EAAE,IAAI;AACtD;AAEO,MAAM,oBAAoB;IAC/B,yBAAyB;IACzB,mBAAmB,CAAC,aAClB,IAAI,GAAG,CAAQ,CAAC,cAAc,EAAE,WAAW,eAAe,CAAC;IAE7D,sBAAsB;IACtB,sBAAsB,CAAC,YAAoB,iBACzC,IAAI,IAAI,CAAM,CAAC,cAAc,EAAE,WAAW,eAAe,CAAC,EAAE;IAE9D,yBAAyB;IACzB,aAAa,CAAC,YAAoB,SAChC,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,WAAW,cAAc,EAAE,QAAQ,EAAE;YAC5D,cAAc;QAChB;AACJ;AAEO,MAAM,WAAW;IACtB,uBAAuB;IACvB,YAAY,CAAC,YAAoB,MAAY;QAC3C,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,aAAa;QAC7B,OAAO,IAAI,IAAI,CAAM,CAAC,WAAW,EAAE,WAAW,YAAY,CAAC,EAAE,UAAU;YACrE,SAAS;gBAAE,gBAAgB;YAAsB;QACnD;IACF;IAEA,yBAAyB;IACzB,cAAc,CAAC,YAAoB;QACjC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,OAAO,IAAI,IAAI,CAAM,CAAC,WAAW,EAAE,WAAW,oBAAoB,CAAC,EAAE,UAAU;YAC7E,SAAS;gBAAE,gBAAgB;YAAsB;QACnD;IACF;IAEA,cAAc;IACd,YAAY,CAAC,aACX,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,YAAY,CAAC,EAAE;YAC9C,cAAc;QAChB;IAEF,gBAAgB;IAChB,cAAc,CAAC,aACb,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,oBAAoB,CAAC,EAAE;YACtD,cAAc;QAChB;AACJ;AAEO,MAAM,aAAa;IACxB,qBAAqB;IACrB,sBAAsB,CAAC,aACrB,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,YAAY,CAAC;IAEvD,aAAa;IACb,cAAc,CAAC,aACb,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,IAAI,CAAC;IAE/C,0CAA0C;IAC1C,yBAAyB,CAAC,aACxB,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,eAAe,CAAC;IAE1D,kBAAkB;IAClB,mBAAmB,CAAC,aAClB,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,SAAS,CAAC;IAEpD,0BAA0B;IAC1B,sBAAsB,CAAC,YAAoB,YAAqB;QAC9D,MAAM,SAAS,IAAI;QACnB,IAAI,YAAY,OAAO,MAAM,CAAC,eAAe;QAC7C,IAAI,UAAU,OAAO,MAAM,CAAC,aAAa;QACzC,MAAM,cAAc,OAAO,QAAQ;QACnC,OAAO,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,aAAa,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IACtG;AACF;AAEO,MAAM,cAAc;IACzB,2BAA2B;IAC3B,aAAa,IACX,IAAI,GAAG,CAAQ;IAEjB,6BAA6B;IAC7B,aAAa,CAAC,KACZ,IAAI,GAAG,CAAM,CAAC,cAAc,EAAE,IAAI;IAEpC,sBAAsB;IACtB,gBAAgB,CAAC,WACf,IAAI,IAAI,CAAM,iBAAiB;IAEjC,oBAAoB;IACpB,gBAAgB,CAAC,IAAY,UAC3B,IAAI,GAAG,CAAM,CAAC,cAAc,EAAE,IAAI,EAAE;IAEtC,+BAA+B;IAC/B,uBAAuB,CAAC,KACtB,IAAI,GAAG,CAQJ,CAAC,cAAc,EAAE,GAAG,WAAW,CAAC;AACvC;AAEO,MAAM,WAAW;IACtB,wCAAwC;IACxC,UAAU,IACR,IAAI,GAAG,CAAQ;IAEjB,2BAA2B;IAC3B,SAAS,CAAC,KACR,IAAI,GAAG,CAAM,CAAC,WAAW,EAAE,IAAI;IAEjC,oBAAoB;IACpB,YAAY,CAAC,OACX,IAAI,IAAI,CAAM,cAAc;IAE9B,kBAAkB;IAClB,YAAY,CAAC,IAAY,UACvB,IAAI,GAAG,CAAM,CAAC,WAAW,EAAE,IAAI,EAAE;IAEnC,iBAAiB;IACjB,YAAY,CAAC,KACX,IAAI,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;IAE/B,4BAA4B;IAC5B,kBAAkB,CAAC,KACjB,IAAI,GAAG,CAAM,CAAC,kBAAkB,EAAE,IAAI;IAExC,0BAA0B;IAC1B,mBAAmB,IACjB,IAAI,GAAG,CAAM;IAEf,mBAAmB;IACnB,iBAAiB,CAAC,SAChB,IAAI,IAAI,CAAM,yBAAyB;YAAE,SAAS;QAAO;IAE3D,4BAA4B;IAC5B,iBAAiB,IACf,IAAI,GAAG,CAAM;IAEf,iBAAiB;IACjB,eAAe,IACb,IAAI,IAAI,CAAM;AAClB;uCAEe", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'\nimport { User, Cantier<PERSON> } from '@/types'\nimport { authApi, usersApi } from '@/lib/api'\n\ninterface AuthContextType {\n  user: User | null\n  cantiere: Cantiere | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  isImpersonating: boolean\n  impersonatedUser: any | null\n  expirationWarning: string | null\n  daysUntilExpiration: number | null\n  expirationDate: string | null\n  login: (username: string, password: string) => Promise<void>\n  loginCantiere: (codice_cantiere: string, password_cantiere: string) => Promise<void>\n  logout: () => void\n  checkAuth: () => Promise<void>\n  impersonateUser: (userId: number) => Promise<any>\n  selectCantiere: (cantiere: Cantiere) => void\n  clearCantiere: () => void\n  dismissExpirationWarning: () => void\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null)\n  const [cantiere, setCantiere] = useState<Cantiere | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [isImpersonating, setIsImpersonating] = useState(() => {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('isImpersonating') === 'true'\n    }\n    return false\n  })\n  const [impersonatedUser, setImpersonatedUser] = useState<any | null>(() => {\n    if (typeof window !== 'undefined') {\n      const stored = localStorage.getItem('impersonatedUser')\n      return stored ? JSON.parse(stored) : null\n    }\n    return null\n  })\n\n  // Stati per warning di scadenza\n  const [expirationWarning, setExpirationWarning] = useState<string | null>(null)\n  const [daysUntilExpiration, setDaysUntilExpiration] = useState<number | null>(null)\n  const [expirationDate, setExpirationDate] = useState<string | null>(null)\n\n  // Definizione più rigorosa di isAuthenticated\n  const isAuthenticated = (!!user && user.id_utente) || (!!cantiere && cantiere.id_cantiere)\n\n  // Stato di autenticazione senza debug log\n\n  // Inizializzazione: verifica se c'è un token valido esistente\n  useEffect(() => {\n    console.log('🔐 AuthContext: Inizializzazione - controllo autenticazione esistente')\n\n    if (typeof window !== 'undefined') {\n      const token = localStorage.getItem('token')\n\n      if (token) {\n        console.log('🔐 AuthContext: Token trovato, verifica validità')\n        // Se c'è un token, verifica se è ancora valido\n        checkAuth()\n      } else {\n        console.log('🔐 AuthContext: Nessun token trovato, richiesto login')\n        // Se non c'è token, imposta loading a false per mostrare login\n        setIsLoading(false)\n        setUser(null)\n        setCantiere(null)\n        setImpersonatedUser(null)\n        setIsImpersonating(false)\n      }\n    } else {\n      // SSR fallback\n      setIsLoading(false)\n    }\n  }, [])\n\n  // Carica il cantiere selezionato dal localStorage all'avvio - MIGLIORATO per evitare race conditions\n  useEffect(() => {\n    if (typeof window !== 'undefined' && user && !isLoading && !cantiere) {\n      const cantiereId = localStorage.getItem('selectedCantiereId')\n      const cantiereName = localStorage.getItem('selectedCantiereName')\n\n      // Validazione robusta dell'ID cantiere\n      if (cantiereId && cantiereId !== 'null' && cantiereId !== 'undefined') {\n        const parsedId = parseInt(cantiereId, 10)\n        if (!isNaN(parsedId) && parsedId > 0) {\n          const cantiereData = {\n            id_cantiere: parsedId,\n            commessa: cantiereName || `Cantiere ${parsedId}`,\n            codice_univoco: '',\n            id_utente: user.id_utente\n          }\n          setCantiere(cantiereData)\n        } else {\n          // Pulisci localStorage se l'ID non è valido\n          localStorage.removeItem('selectedCantiereId')\n          localStorage.removeItem('selectedCantiereName')\n        }\n      }\n    }\n  }, [user, isLoading, cantiere])\n\n  const checkAuth = async () => {\n    try {\n      // Verifica se siamo nel browser\n      if (typeof window === 'undefined') {\n        setIsLoading(false)\n        return\n      }\n\n      // Prima di tutto, imposta loading a true\n      setIsLoading(true)\n\n      // Pulisci eventuali token non validi o scaduti\n      const token = localStorage.getItem('token')\n\n      if (token) {\n        try {\n          // Verifica la validità del token\n          const response = await authApi.verifyToken()\n\n          // Il backend restituisce direttamente i dati, non wrapped in { user: ... }\n          const userData = response\n\n          // Imposta i dati dell'utente come nel sistema React originale\n          const userInfo = {\n            id_utente: userData.user_id,\n            username: userData.username,\n            ruolo: userData.role\n          }\n          setUser(userInfo)\n\n          // Gestisci l'impersonificazione\n          const impersonatingState = userData.is_impersonated === true\n          setIsImpersonating(impersonatingState)\n\n          if (impersonatingState && userData.impersonated_id) {\n            const impersonatedUserData = {\n              id: userData.impersonated_id,\n              username: userData.impersonated_username,\n              role: userData.impersonated_role\n            }\n            setImpersonatedUser(impersonatedUserData)\n            if (typeof window !== 'undefined') {\n              localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData))\n              localStorage.setItem('isImpersonating', 'true')\n            }\n          } else {\n            setImpersonatedUser(null)\n            if (typeof window !== 'undefined') {\n              localStorage.removeItem('impersonatedUser')\n              localStorage.removeItem('isImpersonating')\n            }\n          }\n\n          // Se è un utente cantiere, gestisci i dati del cantiere\n          if (userData.role === 'cantieri_user' && userData.cantiere_id) {\n            const cantiereData = {\n              id_cantiere: userData.cantiere_id,\n              commessa: userData.cantiere_name || `Cantiere ${userData.cantiere_id}`,\n              codice_univoco: '',\n              id_utente: userData.user_id\n            }\n            console.log('🏗️ AuthContext: Impostazione cantiere per utente cantiere:', cantiereData)\n            setCantiere(cantiereData)\n\n            // Sincronizza con localStorage usando il formato standard\n            localStorage.setItem('selectedCantiereId', userData.cantiere_id.toString())\n            localStorage.setItem('selectedCantiereName', cantiereData.commessa)\n          } else {\n            // Per utenti standard, prova a caricare il cantiere dal localStorage\n            console.log('🏗️ AuthContext: Utente standard, controllo cantiere dal localStorage')\n\n            // Controlla se c'è un cantiere salvato dal login cantiere\n            const cantiereData = localStorage.getItem('cantiere_data')\n            if (cantiereData) {\n              try {\n                const parsedCantiere = JSON.parse(cantiereData)\n                console.log('🏗️ AuthContext: Caricamento cantiere da cantiere_data:', parsedCantiere)\n                setCantiere(parsedCantiere)\n\n                // Sincronizza con il formato standard\n                localStorage.setItem('selectedCantiereId', parsedCantiere.id_cantiere.toString())\n                localStorage.setItem('selectedCantiereName', parsedCantiere.commessa)\n              } catch (parseError) {\n                console.warn('🏗️ AuthContext: Errore parsing cantiere_data:', parseError)\n                localStorage.removeItem('cantiere_data')\n              }\n            }\n          }\n        } catch (tokenError) {\n          console.error('🔐 AuthContext: Token non valido:', tokenError)\n          // Se il token non è valido, rimuovilo e pulisci tutto\n          if (typeof window !== 'undefined') {\n            localStorage.removeItem('token')\n            localStorage.removeItem('access_token')\n            localStorage.removeItem('user_data')\n            localStorage.removeItem('cantiere_data')\n            localStorage.removeItem('selectedCantiereId')\n            localStorage.removeItem('selectedCantiereName')\n            localStorage.removeItem('isImpersonating')\n            localStorage.removeItem('impersonatedUser')\n          }\n          setUser(null)\n          setCantiere(null)\n          setIsImpersonating(false)\n          setImpersonatedUser(null)\n        }\n      } else {\n        console.log('🔐 AuthContext: Nessun token trovato')\n        setUser(null)\n        setCantiere(null)\n      }\n    } catch (error) {\n      console.error('🔐 AuthContext: Errore generale durante checkAuth:', error)\n      // In caso di errore generale, rimuovi solo i dati di autenticazione\n      if (typeof window !== 'undefined') {\n        // Rimuovi solo i dati di autenticazione, non tutto il localStorage\n        localStorage.removeItem('token')\n        localStorage.removeItem('access_token')\n        localStorage.removeItem('user_data')\n        localStorage.removeItem('cantiere_data')\n        localStorage.removeItem('isImpersonating')\n        localStorage.removeItem('impersonatedUser')\n        // Mantieni selectedCantiereId e selectedCantiereName per il prossimo login\n      }\n      setUser(null)\n      setCantiere(null)\n      setIsImpersonating(false)\n      setImpersonatedUser(null)\n    } finally {\n      // Assicurati che loading sia impostato a false alla fine\n      console.log('🔐 AuthContext: checkAuth completato, impostazione loading = false')\n      setIsLoading(false)\n    }\n  }\n\n  const login = async (username: string, password: string) => {\n    try {\n      console.log('🔐 AuthContext: Inizio login per:', username)\n      setIsLoading(true)\n      const response = await authApi.login({ username, password })\n      console.log('📡 AuthContext: Risposta backend ricevuta:', response)\n\n      if (typeof window !== 'undefined') {\n        // Salva il token come nel sistema React originale\n        localStorage.setItem('token', response.access_token)\n        console.log('💾 AuthContext: Token salvato nel localStorage')\n\n        // Il backend restituisce i dati dell'utente direttamente nella risposta\n        const userData = {\n          id_utente: response.user_id,\n          username: response.username,\n          ruolo: response.role\n        }\n\n        // Gestisci warning di scadenza se presenti\n        if (response.expiration_warning) {\n          console.log('⚠️ AuthContext: Warning scadenza ricevuto:', response.expiration_warning)\n          setExpirationWarning(response.expiration_warning)\n          setDaysUntilExpiration(response.days_until_expiration)\n          setExpirationDate(response.expiration_date)\n        } else {\n          // Pulisci warning precedenti\n          setExpirationWarning(null)\n          setDaysUntilExpiration(null)\n          setExpirationDate(null)\n        }\n\n        console.log('👤 AuthContext: Dati utente creati:', userData)\n        setUser(userData)\n        setCantiere(null)\n        console.log('✅ AuthContext: Stato utente aggiornato, restituisco userData')\n\n        return userData\n      }\n\n      // Fallback se window non è disponibile (SSR)\n      console.log('🔄 AuthContext: Fallback SSR')\n      return {\n        id_utente: response.user_id,\n        username: response.username,\n        ruolo: response.role\n      }\n    } catch (error) {\n      console.error('❌ AuthContext: Errore durante login:', error)\n      throw error\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const loginCantiere = async (codice_cantiere: string, password_cantiere: string) => {\n    try {\n      console.log('🔐 AuthContext: Inizio login cantiere:', codice_cantiere)\n      setIsLoading(true)\n      const response = await authApi.loginCantiere({ codice_cantiere, password_cantiere })\n      console.log('🔐 AuthContext: Risposta login cantiere:', response)\n\n      if (typeof window !== 'undefined') {\n        // Salva il token come nel sistema React originale\n        localStorage.setItem('token', response.access_token)\n        console.log('🔐 AuthContext: Token salvato')\n\n        // Il backend restituisce i dati del cantiere direttamente nella risposta\n        const cantiereData = {\n          id_cantiere: response.cantiere_id,\n          commessa: response.cantiere_name,\n          codice_univoco: codice_cantiere,\n          id_utente: response.user_id\n        }\n        console.log('🔐 AuthContext: Dati cantiere preparati:', cantiereData)\n\n        // Salva i dati del cantiere nel localStorage\n        localStorage.setItem('cantiere_data', JSON.stringify(cantiereData))\n        console.log('🔐 AuthContext: Dati cantiere salvati in localStorage')\n\n        setCantiere(cantiereData)\n        setUser(null)\n        console.log('🔐 AuthContext: Context aggiornato')\n\n        // Forza il refresh dell'autenticazione per assicurarsi che tutto sia sincronizzato\n        await checkAuth()\n        console.log('🔐 AuthContext: checkAuth completato')\n\n        return cantiereData\n      }\n\n      // Fallback se window non è disponibile (SSR)\n      return {\n        id_cantiere: response.cantiere_id,\n        commessa: response.cantiere_name,\n        codice_univoco: codice_cantiere,\n        id_utente: response.user_id\n      }\n    } catch (error) {\n      throw error\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const impersonateUser = async (userId: number) => {\n    try {\n      // Chiama l'endpoint di impersonificazione\n      const response = await usersApi.impersonateUser(userId)\n\n      if (typeof window !== 'undefined') {\n        // Salva il token nel localStorage\n        localStorage.setItem('token', response.access_token)\n\n        // Salva i dati dell'utente impersonato\n        const impersonatedUserData = {\n          id: response.impersonated_id,\n          username: response.impersonated_username,\n          role: response.impersonated_role\n        }\n\n        // Salva i dati dell'utente impersonato nel localStorage\n        localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData))\n        setImpersonatedUser(impersonatedUserData)\n\n        // Imposta lo stato di impersonificazione a true\n        setIsImpersonating(true)\n        localStorage.setItem('isImpersonating', 'true')\n\n        return { impersonatedUser: impersonatedUserData }\n      }\n    } catch (error) {\n      throw error\n    }\n  }\n\n  // Funzione per selezionare un cantiere - MIGLIORATA per gestione più robusta\n  const selectCantiere = (cantiere: Cantiere) => {\n    if (!cantiere || !cantiere.id_cantiere || cantiere.id_cantiere <= 0) {\n      console.error('🏗️ AuthContext: Tentativo di selezione cantiere non valido:', cantiere)\n      return\n    }\n\n    try {\n      // Usa commessa come nome del cantiere, con fallback su nome se commessa non è disponibile\n      const cantiereName = cantiere.commessa || `Cantiere ${cantiere.id_cantiere}`\n\n      // Salva nel localStorage con validazione\n      localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString())\n      localStorage.setItem('selectedCantiereName', cantiereName)\n\n      // Aggiorna lo stato\n      const cantiereData = {\n        ...cantiere,\n        commessa: cantiereName\n      }\n\n      console.log('🏗️ AuthContext: Cantiere selezionato:', cantiereData)\n      setCantiere(cantiereData)\n\n      // Rimuovi eventuali dati obsoleti\n      localStorage.removeItem('cantiere_data')\n\n    } catch (error) {\n      console.error('🏗️ AuthContext: Errore nella selezione cantiere:', error)\n    }\n  }\n\n  // Funzione per pulire lo stato del cantiere\n  const clearCantiere = () => {\n    console.log('🏗️ AuthContext: Pulizia stato cantiere')\n    setCantiere(null)\n    localStorage.removeItem('selectedCantiereId')\n    localStorage.removeItem('selectedCantiereName')\n    localStorage.removeItem('cantiere_data')\n  }\n\n  const logout = () => {\n    if (typeof window !== 'undefined') {\n      // Logout sempre completo - rimuovi tutto\n      localStorage.clear() // Pulisce tutto il localStorage\n      sessionStorage.clear() // Pulisce anche sessionStorage\n\n      // Reset stati\n      setUser(null)\n      setCantiere(null)\n      setIsImpersonating(false)\n      setImpersonatedUser(null)\n      setExpirationWarning(null)\n      setDaysUntilExpiration(null)\n      setExpirationDate(null)\n\n      // Forza reload completo della pagina per evitare cache\n      window.location.replace('/login')\n    }\n  }\n\n  const dismissExpirationWarning = () => {\n    setExpirationWarning(null)\n    setDaysUntilExpiration(null)\n    setExpirationDate(null)\n  }\n\n  const value: AuthContextType = {\n    user,\n    cantiere,\n    isAuthenticated,\n    isLoading,\n    isImpersonating,\n    impersonatedUser,\n    expirationWarning,\n    daysUntilExpiration,\n    expirationDate,\n    login,\n    loginCantiere,\n    logout,\n    checkAuth,\n    impersonateUser,\n    selectCantiere,\n    clearCantiere,\n    dismissExpirationWarning,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AA0BA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,uCAAmC;;QAEnC;QACA,OAAO;IACT;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACnE,uCAAmC;;QAGnC;QACA,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,8CAA8C;IAC9C,MAAM,kBAAkB,AAAC,CAAC,CAAC,QAAQ,KAAK,SAAS,IAAM,CAAC,CAAC,YAAY,SAAS,WAAW;IAEzF,0CAA0C;IAE1C,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC;QAEZ,uCAAmC;;QAgBnC,OAAO;YACL,eAAe;YACf,aAAa;QACf;IACF,GAAG,EAAE;IAEL,qGAAqG;IACrG,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAsE;;QAqBtE;IACF,GAAG;QAAC;QAAM;QAAW;KAAS;IAE9B,MAAM,YAAY;QAChB,IAAI;YACF,gCAAgC;YAChC,wCAAmC;gBACjC,aAAa;gBACb;YACF;;YAKA,+CAA+C;YAC/C,MAAM;QAmGR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sDAAsD;YACpE,oEAAoE;YACpE,uCAAmC;;YAQjC,2EAA2E;YAC7E;YACA,QAAQ;YACR,YAAY;YACZ,mBAAmB;YACnB,oBAAoB;QACtB,SAAU;YACR,yDAAyD;YACzD,QAAQ,GAAG,CAAC;YACZ,aAAa;QACf;IACF;IAEA,MAAM,QAAQ,OAAO,UAAkB;QACrC,IAAI;YACF,QAAQ,GAAG,CAAC,qCAAqC;YACjD,aAAa;YACb,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAAE;gBAAU;YAAS;YAC1D,QAAQ,GAAG,CAAC,8CAA8C;YAE1D,uCAAmC;;YA+BnC;YAEA,6CAA6C;YAC7C,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,WAAW,SAAS,OAAO;gBAC3B,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,OAAO,iBAAyB;QACpD,IAAI;YACF,QAAQ,GAAG,CAAC,0CAA0C;YACtD,aAAa;YACb,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,aAAa,CAAC;gBAAE;gBAAiB;YAAkB;YAClF,QAAQ,GAAG,CAAC,4CAA4C;YAExD,uCAAmC;;YA2BnC;YAEA,6CAA6C;YAC7C,OAAO;gBACL,aAAa,SAAS,WAAW;gBACjC,UAAU,SAAS,aAAa;gBAChC,gBAAgB;gBAChB,WAAW,SAAS,OAAO;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,0CAA0C;YAC1C,MAAM,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,eAAe,CAAC;YAEhD,uCAAmC;;YAoBnC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,6EAA6E;IAC7E,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,YAAY,CAAC,SAAS,WAAW,IAAI,SAAS,WAAW,IAAI,GAAG;YACnE,QAAQ,KAAK,CAAC,gEAAgE;YAC9E;QACF;QAEA,IAAI;YACF,0FAA0F;YAC1F,MAAM,eAAe,SAAS,QAAQ,IAAI,CAAC,SAAS,EAAE,SAAS,WAAW,EAAE;YAE5E,yCAAyC;YACzC,aAAa,OAAO,CAAC,sBAAsB,SAAS,WAAW,CAAC,QAAQ;YACxE,aAAa,OAAO,CAAC,wBAAwB;YAE7C,oBAAoB;YACpB,MAAM,eAAe;gBACnB,GAAG,QAAQ;gBACX,UAAU;YACZ;YAEA,QAAQ,GAAG,CAAC,0CAA0C;YACtD,YAAY;YAEZ,kCAAkC;YAClC,aAAa,UAAU,CAAC;QAE1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qDAAqD;QACrE;IACF;IAEA,4CAA4C;IAC5C,MAAM,gBAAgB;QACpB,QAAQ,GAAG,CAAC;QACZ,YAAY;QACZ,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,SAAS;QACb,uCAAmC;;QAgBnC;IACF;IAEA,MAAM,2BAA2B;QAC/B,qBAAqB;QACrB,uBAAuB;QACvB,kBAAkB;IACpB;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 774, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { PrimaryButton, QuickButton } from '@/components/ui/animated-button'\nimport { Badge } from '@/components/ui/badge'\nimport { useAuth } from '@/contexts/AuthContext'\nimport {\n  Cable,\n  Home,\n  Activity,\n  BarChart3,\n  Settings,\n  Users,\n  Menu,\n  X,\n  Building2,\n  ClipboardList,\n  FileText,\n  LogOut,\n  Package,\n  User,\n\n} from 'lucide-react'\n\nconst getNavigation = (userRole: string | undefined, isImpersonating: boolean, impersonatedUser: any, cantiereId?: number) => {\n  // Home button - testo personalizzato come nella webapp originale\n  const homeButton = {\n    name: userRole === 'owner' ? \"Menu Admin\" :\n          userRole === 'user' ? \"Lista Cantieri\" :\n          userRole === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\",\n    href: userRole === 'owner' ? '/admin' :\n          userRole === 'user' ? '/cantieri' :\n          userRole === 'cantieri_user' ? '/cavi' : '/',\n    icon: Home\n  }\n\n  if (userRole === 'owner' && !isImpersonating) {\n    // Solo amministratore - solo il pulsante Home che va al pannello admin\n    return [homeButton]\n  }\n\n  if (userRole === 'user' || (isImpersonating && impersonatedUser?.role === 'user')) {\n    // Utente standard - Home + eventualmente cantieri se impersonificato\n    const nav = [homeButton]\n    if (isImpersonating) {\n      nav.push({ name: 'Cantieri', href: '/cantieri', icon: Building2 })\n    }\n\n    // Se un cantiere è selezionato, aggiungi i menu di gestione come nella webapp originale\n    if (cantiereId) {\n      nav.push(\n        { name: 'Visualizza Cavi', href: '/cavi', icon: Cable },\n        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },\n        { name: 'Gestione Excel', href: '/excel', icon: FileText },\n        { name: 'Report', href: '/reports', icon: BarChart3 },\n        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },\n        { name: 'Produttività', href: '/productivity', icon: Activity },\n      )\n    }\n\n    return nav\n  }\n\n  if (userRole === 'cantieri_user' || (isImpersonating && impersonatedUser?.role === 'cantieri_user')) {\n    // Utente cantiere - menu completo come nella webapp originale\n    const nav = [homeButton]\n\n    // Se un cantiere è selezionato, aggiungi i menu di gestione\n    if (cantiereId) {\n      // Se non è cantieri_user diretto, aggiungi Visualizza Cavi\n      if (userRole !== 'cantieri_user') {\n        nav.push({ name: 'Visualizza Cavi', href: '/cavi', icon: Cable })\n      }\n\n      nav.push(\n        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },\n        { name: 'Gestione Excel', href: '/excel', icon: FileText },\n        { name: 'Report', href: '/reports', icon: BarChart3 },\n        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },\n        { name: 'Produttività', href: '/productivity', icon: Activity },\n      )\n    }\n\n    return nav\n  }\n\n  // Default\n  return [homeButton]\n}\n\nexport function Navbar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, cantiere, isAuthenticated, isImpersonating, impersonatedUser, logout } = useAuth()\n\n  // Recupera l'ID del cantiere selezionato dal localStorage o dal context\n  const cantiereId = cantiere?.id_cantiere || (typeof window !== 'undefined' ? parseInt(localStorage.getItem('selectedCantiereId') || '0') : 0)\n  const cantiereName = cantiere?.commessa || (typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereName') : '') || `Cantiere ${cantiereId}`\n\n  const navigation = getNavigation(user?.ruolo, isImpersonating, impersonatedUser, cantiereId)\n\n\n\n  // Non mostrare navbar nella pagina di login\n  if (pathname === '/login') {\n    return null\n  }\n\n  // Se non autenticato, non mostrare navbar\n  if (!isAuthenticated) {\n    return null\n  }\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm\">\n      <div className=\"max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n\n          {/* Tutto a sinistra: Logo + Menu Admin + Navigation */}\n          <div className=\"flex items-center space-x-6\">\n            {/* Logo e Brand */}\n            <div className=\"flex items-center space-x-3 cursor-default\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center\">\n                <Cable className=\"w-5 h-5 text-white\" />\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"text-xl font-bold text-slate-900\">CABLYS</h1>\n                <p className=\"text-xs text-slate-500 -mt-1\">Cable Installation System</p>\n              </div>\n            </div>\n\n\n\n            {/* Navigation Desktop - allontanata dal logo */}\n            <div className=\"hidden md:flex items-center space-x-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href ||\n                             (item.href !== '/' && pathname.startsWith(item.href))\n              const Icon = item.icon\n\n              return (\n                <Link key={item.name} href={item.href}>\n                  <div className={`flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md ${\n                    isActive\n                      ? 'text-blue-700 bg-blue-50 border border-blue-200 font-medium'\n                      : 'text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200 border border-transparent'\n                  }`}>\n                    <Icon className=\"w-4 h-4\" />\n                    <span className=\"hidden lg:inline\">{item.name}</span>\n                  </div>\n                </Link>\n              )\n            })}\n            </div>\n          </div>\n\n          {/* User Info a destra con più margine */}\n          <div className=\"flex items-center space-x-4 ml-8\">\n            {/* Display cantiere selezionato - versione compatta */}\n            {cantiereId && cantiereId > 0 && (\n              <div className=\"hidden sm:flex items-center space-x-2 px-2 py-1 bg-blue-50 border border-blue-200 rounded-md\">\n                <Building2 className=\"w-3 h-3 text-blue-600\" />\n                <div className=\"text-xs\">\n                  <span className=\"text-blue-900 font-medium\">{cantiereName}</span>\n                </div>\n              </div>\n            )}\n\n            {/* User Info e Logout */}\n            <div className=\"hidden sm:flex items-center space-x-3\">\n              <div className=\"text-right\">\n                <p className=\"text-sm font-medium text-slate-900\">\n                  {isImpersonating && impersonatedUser ? impersonatedUser.username : user?.username}\n                </p>\n              </div>\n              <div className=\"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\">\n                <User className=\"w-3 h-3 text-white\" />\n              </div>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md border border-transparent text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2\"\n                onClick={logout}\n                title=\"Logout\"\n              >\n                <LogOut className=\"w-4 h-4\" />\n                <span className=\"hidden lg:inline\">Logout</span>\n              </Button>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"text-slate-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 ease-in-out rounded-md\"\n              >\n                {isOpen ? <X className=\"w-5 h-5\" /> : <Menu className=\"w-5 h-5\" />}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isOpen && (\n        <div className=\"md:hidden border-t border-slate-200 bg-white\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href || \n                             (item.href !== '/' && pathname.startsWith(item.href))\n              const Icon = item.icon\n              \n              return (\n                <Link key={item.name} href={item.href}>\n                  <div\n                    className={`w-full flex items-center justify-start space-x-3 px-3 py-2 transition-all duration-200 ease-in-out rounded-md ${\n                      isActive\n                        ? 'text-blue-700 bg-blue-50 border border-blue-200 font-medium'\n                        : 'text-slate-600 hover:text-slate-900 hover:bg-blue-50 border border-transparent'\n                    }`}\n                    onClick={() => setIsOpen(false)}\n                  >\n                    <Icon className=\"w-4 h-4\" />\n                    <span>{item.name}</span>\n                  </div>\n                </Link>\n              )\n            })}\n          </div>\n          \n          {/* Mobile User Info e Actions */}\n          <div className=\"border-t border-slate-200 px-4 py-3\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\">\n                  {user ? <User className=\"w-3 h-3 text-white\" /> : <Building2 className=\"w-3 h-3 text-white\" />}\n                </div>\n                <div>\n                  <p className=\"text-sm font-medium text-slate-900\">\n                    {isImpersonating && impersonatedUser ? impersonatedUser.username : (user ? user.username : cantiere?.commessa)}\n                  </p>\n                </div>\n              </div>\n\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={logout}\n                title=\"Logout\"\n                className=\"hover:bg-red-50 hover:text-red-600 transition-all duration-200 ease-in-out rounded-md\"\n              >\n                <LogOut className=\"w-4 h-4\" />\n              </Button>\n            </div>\n\n            {/* Mobile Admin Menu - solo per owner */}\n            {user?.ruolo === 'owner' && !isImpersonating && (\n              <div className=\"mt-3 pt-3 border-t border-slate-200\">\n                <p className=\"text-xs font-medium text-slate-500 mb-2\">AMMINISTRAZIONE</p>\n                <div className=\"space-y-1\">\n                  <Link\n                    href=\"/admin\"\n                    className=\"block px-3 py-2 text-sm text-slate-700 hover:bg-slate-100 rounded-md transition-colors duration-150\"\n                    onClick={() => setIsOpen(false)}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <Users className=\"w-4 h-4 text-slate-500\" />\n                      <span>Pannello Admin</span>\n                    </div>\n                  </Link>\n                  <Link\n                    href=\"/admin?tab=users\"\n                    className=\"block px-3 py-2 text-sm text-slate-700 hover:bg-slate-100 rounded-md transition-colors duration-150\"\n                    onClick={() => setIsOpen(false)}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <Users className=\"w-4 h-4 text-slate-500\" />\n                      <span>Gestione Utenti</span>\n                    </div>\n                  </Link>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;AA2BA,MAAM,gBAAgB,CAAC,UAA8B,iBAA0B,kBAAuB;IACpG,iEAAiE;IACjE,MAAM,aAAa;QACjB,MAAM,aAAa,UAAU,eACvB,aAAa,SAAS,mBACtB,aAAa,kBAAkB,kBAAkB;QACvD,MAAM,aAAa,UAAU,WACvB,aAAa,SAAS,cACtB,aAAa,kBAAkB,UAAU;QAC/C,MAAM,mMAAA,CAAA,OAAI;IACZ;IAEA,IAAI,aAAa,WAAW,CAAC,iBAAiB;QAC5C,uEAAuE;QACvE,OAAO;YAAC;SAAW;IACrB;IAEA,IAAI,aAAa,UAAW,mBAAmB,kBAAkB,SAAS,QAAS;QACjF,qEAAqE;QACrE,MAAM,MAAM;YAAC;SAAW;QACxB,IAAI,iBAAiB;YACnB,IAAI,IAAI,CAAC;gBAAE,MAAM;gBAAY,MAAM;gBAAa,MAAM,gNAAA,CAAA,YAAS;YAAC;QAClE;QAEA,wFAAwF;QACxF,IAAI,YAAY;YACd,IAAI,IAAI,CACN;gBAAE,MAAM;gBAAmB,MAAM;gBAAS,MAAM,oMAAA,CAAA,QAAK;YAAC,GACtD;gBAAE,MAAM;gBAAc,MAAM;gBAAe,MAAM,wMAAA,CAAA,UAAO;YAAC,GACzD;gBAAE,MAAM;gBAAkB,MAAM;gBAAU,MAAM,8MAAA,CAAA,WAAQ;YAAC,GACzD;gBAAE,MAAM;gBAAU,MAAM;gBAAY,MAAM,kNAAA,CAAA,YAAS;YAAC,GACpD;gBAAE,MAAM;gBAAoB,MAAM;gBAAY,MAAM,wNAAA,CAAA,gBAAa;YAAC,GAClE;gBAAE,MAAM;gBAAgB,MAAM;gBAAiB,MAAM,0MAAA,CAAA,WAAQ;YAAC;QAElE;QAEA,OAAO;IACT;IAEA,IAAI,aAAa,mBAAoB,mBAAmB,kBAAkB,SAAS,iBAAkB;QACnG,8DAA8D;QAC9D,MAAM,MAAM;YAAC;SAAW;QAExB,4DAA4D;QAC5D,IAAI,YAAY;YACd,2DAA2D;YAC3D,IAAI,aAAa,iBAAiB;gBAChC,IAAI,IAAI,CAAC;oBAAE,MAAM;oBAAmB,MAAM;oBAAS,MAAM,oMAAA,CAAA,QAAK;gBAAC;YACjE;YAEA,IAAI,IAAI,CACN;gBAAE,MAAM;gBAAc,MAAM;gBAAe,MAAM,wMAAA,CAAA,UAAO;YAAC,GACzD;gBAAE,MAAM;gBAAkB,MAAM;gBAAU,MAAM,8MAAA,CAAA,WAAQ;YAAC,GACzD;gBAAE,MAAM;gBAAU,MAAM;gBAAY,MAAM,kNAAA,CAAA,YAAS;YAAC,GACpD;gBAAE,MAAM;gBAAoB,MAAM;gBAAY,MAAM,wNAAA,CAAA,gBAAa;YAAC,GAClE;gBAAE,MAAM;gBAAgB,MAAM;gBAAiB,MAAM,0MAAA,CAAA,WAAQ;YAAC;QAElE;QAEA,OAAO;IACT;IAEA,UAAU;IACV,OAAO;QAAC;KAAW;AACrB;AAEO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE7F,wEAAwE;IACxE,MAAM,aAAa,UAAU,eAAe,CAAC,6EAA8F,CAAC;IAC5I,MAAM,eAAe,UAAU,YAAY,CAAC,6EAA+E,EAAE,KAAK,CAAC,SAAS,EAAE,YAAY;IAE1J,MAAM,aAAa,cAAc,MAAM,OAAO,iBAAiB,kBAAkB;IAIjF,4CAA4C;IAC5C,IAAI,aAAa,UAAU;QACzB,OAAO;IACT;IAEA,0CAA0C;IAC1C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;8CAOhD,8OAAC;oCAAI,WAAU;8CACd,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACvB,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;wCAClE,MAAM,OAAO,KAAK,IAAI;wCAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAAiB,MAAM,KAAK,IAAI;sDACnC,cAAA,8OAAC;gDAAI,WAAW,CAAC,yFAAyF,EACxG,WACI,gEACA,wGACJ;;kEACA,8OAAC;wDAAK,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEAAoB,KAAK,IAAI;;;;;;;;;;;;2CAPtC,KAAK,IAAI;;;;;oCAWxB;;;;;;;;;;;;sCAKF,8OAAC;4BAAI,WAAU;;gCAEZ,cAAc,aAAa,mBAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;;;;;;8CAMnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DACV,mBAAmB,mBAAmB,iBAAiB,QAAQ,GAAG,MAAM;;;;;;;;;;;sDAG7E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS;4CACT,OAAM;;8DAEN,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,UAAU,CAAC;wCAC1B,WAAU;kDAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ/D,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACvB,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;4BAClE,MAAM,OAAO,KAAK,IAAI;4BAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,8OAAC;oCACC,WAAW,CAAC,8GAA8G,EACxH,WACI,gEACA,kFACJ;oCACF,SAAS,IAAM,UAAU;;sDAEzB,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;;;;;;+BAVT,KAAK,IAAI;;;;;wBAcxB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;yEAA0B,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEzE,8OAAC;0DACC,cAAA,8OAAC;oDAAE,WAAU;8DACV,mBAAmB,mBAAmB,iBAAiB,QAAQ,GAAI,OAAO,KAAK,QAAQ,GAAG,UAAU;;;;;;;;;;;;;;;;;kDAK3G,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,OAAM;wCACN,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAKrB,MAAM,UAAU,WAAW,CAAC,iCAC3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA0C;;;;;;kDACvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,UAAU;0DAEzB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;0DAGV,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,UAAU;0DAEzB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5B", "debugId": null}}, {"offset": {"line": 1406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/hooks/useSessionPersistence.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\n\n/**\n * Hook per gestire la persistenza della sessione e prevenire stati \"fantasma\"\n * Risolve il bug dove l'utente appare loggato ma i reindirizzamenti non funzionano\n */\nexport const useSessionPersistence = () => {\n  const { isAuthenticated, user, cantiere, checkAuth, logout } = useAuth()\n  const heartbeatInterval = useRef<NodeJS.Timeout | null>(null)\n  const lastActivityTime = useRef<number>(Date.now())\n\n  // Aggiorna il timestamp dell'ultima attività\n  const updateActivity = () => {\n    lastActivityTime.current = Date.now()\n  }\n\n  // Verifica periodicamente la validità della sessione\n  const startHeartbeat = () => {\n    if (heartbeatInterval.current) {\n      clearInterval(heartbeatInterval.current)\n    }\n\n    heartbeatInterval.current = setInterval(async () => {\n      try {\n        // Verifica se la sessione è ancora valida\n        if (isAuthenticated && (user || cantiere)) {\n          console.log('🔄 SessionPersistence: Verifica heartbeat sessione')\n          await checkAuth()\n        }\n      } catch (error) {\n        console.error('🔄 SessionPersistence: Errore durante heartbeat:', error)\n        // Se il heartbeat fallisce, forza il logout per evitare stati inconsistenti\n        logout()\n      }\n    }, 5 * 60 * 1000) // Ogni 5 minuti\n  }\n\n  // Ferma il heartbeat\n  const stopHeartbeat = () => {\n    if (heartbeatInterval.current) {\n      clearInterval(heartbeatInterval.current)\n      heartbeatInterval.current = null\n    }\n  }\n\n  // Gestisce la visibilità della pagina (quando l'utente torna alla tab)\n  const handleVisibilityChange = async () => {\n    if (document.visibilityState === 'visible' && isAuthenticated) {\n      console.log('🔄 SessionPersistence: Pagina tornata visibile, verifica sessione')\n      try {\n        await checkAuth()\n      } catch (error) {\n        console.error('🔄 SessionPersistence: Errore durante verifica visibilità:', error)\n        logout()\n      }\n    }\n  }\n\n  // Gestisce il focus della finestra\n  const handleWindowFocus = async () => {\n    if (isAuthenticated) {\n      console.log('🔄 SessionPersistence: Finestra tornata in focus, verifica sessione')\n      try {\n        await checkAuth()\n      } catch (error) {\n        console.error('🔄 SessionPersistence: Errore durante verifica focus:', error)\n        logout()\n      }\n    }\n  }\n\n  // Gestisce la chiusura della finestra/tab\n  const handleBeforeUnload = () => {\n    // Salva il timestamp dell'ultima attività\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('lastActivity', lastActivityTime.current.toString())\n    }\n  }\n\n  // Verifica se la sessione è scaduta per inattività\n  const checkSessionExpiry = () => {\n    if (typeof window !== 'undefined' && isAuthenticated) {\n      const lastActivity = localStorage.getItem('lastActivity')\n      if (lastActivity) {\n        const timeDiff = Date.now() - parseInt(lastActivity, 10)\n        const maxInactivity = 24 * 60 * 60 * 1000 // 24 ore\n\n        if (timeDiff > maxInactivity) {\n          console.log('🔄 SessionPersistence: Sessione scaduta per inattività')\n          logout()\n          return false\n        }\n      }\n    }\n    return true\n  }\n\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    // Se non autenticato, non fare nulla\n    if (!isAuthenticated) {\n      console.log('🔄 SessionPersistence: Utente non autenticato, skip')\n      stopHeartbeat()\n      return\n    }\n\n    // Verifica la scadenza della sessione all'avvio\n    if (!checkSessionExpiry()) {\n      return\n    }\n\n    console.log('🔄 SessionPersistence: Attivazione per utente autenticato')\n\n    // Avvia il heartbeat per sessioni autenticate\n    startHeartbeat()\n\n    // Aggiungi event listeners\n    document.addEventListener('visibilitychange', handleVisibilityChange)\n    window.addEventListener('focus', handleWindowFocus)\n    window.addEventListener('beforeunload', handleBeforeUnload)\n\n    // Event listeners per tracciare l'attività dell'utente\n    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']\n    activityEvents.forEach(event => {\n      document.addEventListener(event, updateActivity, { passive: true })\n    })\n\n    return () => {\n      // Cleanup\n      console.log('🔄 SessionPersistence: Cleanup event listeners')\n      stopHeartbeat()\n      document.removeEventListener('visibilitychange', handleVisibilityChange)\n      window.removeEventListener('focus', handleWindowFocus)\n      window.removeEventListener('beforeunload', handleBeforeUnload)\n\n      activityEvents.forEach(event => {\n        document.removeEventListener(event, updateActivity)\n      })\n    }\n  }, [isAuthenticated, user, cantiere])\n\n  // Cleanup finale\n  useEffect(() => {\n    return () => {\n      stopHeartbeat()\n    }\n  }, [])\n\n  return {\n    updateActivity,\n    checkSessionExpiry\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AASO,MAAM,wBAAwB;IACnC,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACrE,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACxD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU,KAAK,GAAG;IAEhD,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,iBAAiB,OAAO,GAAG,KAAK,GAAG;IACrC;IAEA,qDAAqD;IACrD,MAAM,iBAAiB;QACrB,IAAI,kBAAkB,OAAO,EAAE;YAC7B,cAAc,kBAAkB,OAAO;QACzC;QAEA,kBAAkB,OAAO,GAAG,YAAY;YACtC,IAAI;gBACF,0CAA0C;gBAC1C,IAAI,mBAAmB,CAAC,QAAQ,QAAQ,GAAG;oBACzC,QAAQ,GAAG,CAAC;oBACZ,MAAM;gBACR;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oDAAoD;gBAClE,4EAA4E;gBAC5E;YACF;QACF,GAAG,IAAI,KAAK,MAAM,gBAAgB;;IACpC;IAEA,qBAAqB;IACrB,MAAM,gBAAgB;QACpB,IAAI,kBAAkB,OAAO,EAAE;YAC7B,cAAc,kBAAkB,OAAO;YACvC,kBAAkB,OAAO,GAAG;QAC9B;IACF;IAEA,uEAAuE;IACvE,MAAM,yBAAyB;QAC7B,IAAI,SAAS,eAAe,KAAK,aAAa,iBAAiB;YAC7D,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8DAA8D;gBAC5E;YACF;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM,oBAAoB;QACxB,IAAI,iBAAiB;YACnB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yDAAyD;gBACvE;YACF;QACF;IACF;IAEA,0CAA0C;IAC1C,MAAM,qBAAqB;QACzB,0CAA0C;QAC1C,uCAAmC;;QAEnC;IACF;IAEA,mDAAmD;IACnD,MAAM,qBAAqB;QACzB,uCAAsD;;QAYtD;QACA,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAwBnC,uDAAuD;QACvD,MAAM;IAiBR,GAAG;QAAC;QAAiB;QAAM;KAAS;IAEpC,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL;QACF;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1578, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/auth/ExpirationWarning.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Button } from '@/components/ui/button'\nimport { AlertTriangle, X, Calendar, Clock } from 'lucide-react'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function ExpirationWarning() {\n  const { \n    expirationWarning, \n    daysUntilExpiration, \n    expirationDate, \n    dismissExpirationWarning \n  } = useAuth()\n\n  if (!expirationWarning) {\n    return null\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('it-IT', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric'\n    })\n  }\n\n  const getAlertVariant = () => {\n    if (daysUntilExpiration === 0) return 'destructive'\n    if (daysUntilExpiration === 1) return 'destructive'\n    return 'default'\n  }\n\n  const getIcon = () => {\n    if (daysUntilExpiration === 0) return <AlertTriangle className=\"h-4 w-4\" />\n    if (daysUntilExpiration === 1) return <Clock className=\"h-4 w-4\" />\n    return <Calendar className=\"h-4 w-4\" />\n  }\n\n  return (\n    <Alert variant={getAlertVariant()} className=\"mb-4 border-l-4\">\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex items-start space-x-2\">\n          {getIcon()}\n          <div className=\"flex-1\">\n            <AlertDescription className=\"font-medium\">\n              {expirationWarning}\n            </AlertDescription>\n            {expirationDate && (\n              <AlertDescription className=\"text-sm mt-1 opacity-90\">\n                Data di scadenza: {formatDate(expirationDate)}\n              </AlertDescription>\n            )}\n            <AlertDescription className=\"text-sm mt-2 opacity-80\">\n              Contatta l'amministratore per rinnovare il tuo account.\n            </AlertDescription>\n          </div>\n        </div>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={dismissExpirationWarning}\n          className=\"h-6 w-6 p-0 hover:bg-transparent\"\n        >\n          <X className=\"h-4 w-4\" />\n        </Button>\n      </div>\n    </Alert>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,EACJ,iBAAiB,EACjB,mBAAmB,EACnB,cAAc,EACd,wBAAwB,EACzB,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEV,IAAI,CAAC,mBAAmB;QACtB,OAAO;IACT;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,KAAK;YACL,OAAO;YACP,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,wBAAwB,GAAG,OAAO;QACtC,IAAI,wBAAwB,GAAG,OAAO;QACtC,OAAO;IACT;IAEA,MAAM,UAAU;QACd,IAAI,wBAAwB,GAAG,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QAC/D,IAAI,wBAAwB,GAAG,qBAAO,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvD,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,qBACE,8OAAC,iIAAA,CAAA,QAAK;QAAC,SAAS;QAAmB,WAAU;kBAC3C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;wBACZ;sCACD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CACzB;;;;;;gCAEF,gCACC,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;;wCAA0B;wCACjC,WAAW;;;;;;;8CAGlC,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAA0B;;;;;;;;;;;;;;;;;;8BAK1D,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKvB", "debugId": null}}, {"offset": {"line": 1722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/layout/MainContent.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useSessionPersistence } from '@/hooks/useSessionPersistence'\nimport ExpirationWarning from '@/components/auth/ExpirationWarning'\n\ninterface MainContentProps {\n  children: React.ReactNode\n}\n\nexport default function MainContent({ children }: MainContentProps) {\n  const { isAuthenticated } = useAuth()\n\n  // SEMPRE chiamare gli hooks - la logica condizionale è DENTRO l'hook\n  useSessionPersistence()\n\n  return (\n    <main className=\"pt-16\">\n      {isAuthenticated && (\n        <div className=\"container mx-auto px-4 py-2\">\n          <ExpirationWarning />\n        </div>\n      )}\n      {children}\n    </main>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAWe,SAAS,YAAY,EAAE,QAAQ,EAAoB;IAChE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAElC,qEAAqE;IACrE,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD;IAEpB,qBACE,8OAAC;QAAK,WAAU;;YACb,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,+IAAA,CAAA,UAAiB;;;;;;;;;;YAGrB;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/hooks/use-toast.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nexport interface Toast {\n  id: string\n  title?: string\n  description?: string\n  variant?: 'default' | 'destructive'\n  duration?: number\n}\n\ninterface ToastState {\n  toasts: Toast[]\n}\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_VALUE\n  return count.toString()\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: ToastState, action: any): ToastState => {\n  switch (action.type) {\n    case 'ADD_TOAST':\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case 'UPDATE_TOAST':\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action\n\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case 'REMOVE_TOAST':\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: ToastState) => void> = []\n\nlet memoryState: ToastState = { toasts: [] }\n\nfunction dispatch(action: any) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast2 = Omit<Toast, 'id'>\n\nfunction toast({ ...props }: Toast2) {\n  const id = genId()\n\n  const update = (props: Partial<Toast>) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id })\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open: boolean) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = useState<ToastState>(memoryState)\n\n  useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;AAgBA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAE3B,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,SAAS;IACtC,OAAO,MAAM,QAAQ;AACvB;AAEA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAmB;IACzC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAAgD,EAAE;AAExD,IAAI,cAA0B;IAAE,QAAQ,EAAE;AAAC;AAE3C,SAAS,SAAS,MAAW;IAC3B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAe;IACjC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG,EAAE;IAEL,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF", "debugId": null}}, {"offset": {"line": 1912, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/toaster.tsx"], "sourcesContent": ["'use client'\n\nimport { useToast } from '@/hooks/use-toast'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Button } from '@/components/ui/button'\nimport { X, CheckCircle, AlertCircle } from 'lucide-react'\n\nexport function Toaster() {\n  const { toasts, dismiss } = useToast()\n\n  return (\n    <div className=\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\">\n      {toasts.map((toast) => (\n        <div\n          key={toast.id}\n          className=\"group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\"\n          style={{\n            backgroundColor: toast.variant === 'destructive' ? '#fef2f2' : '#f0f9ff',\n            borderColor: toast.variant === 'destructive' ? '#fecaca' : '#bae6fd',\n          }}\n        >\n          <div className=\"flex items-start space-x-2\">\n            {toast.variant === 'destructive' ? (\n              <AlertCircle className=\"h-4 w-4 text-red-600 mt-0.5\" />\n            ) : (\n              <CheckCircle className=\"h-4 w-4 text-green-600 mt-0.5\" />\n            )}\n            <div className=\"grid gap-1\">\n              {toast.title && (\n                <div className={`text-sm font-semibold ${\n                  toast.variant === 'destructive' ? 'text-red-900' : 'text-gray-900'\n                }`}>\n                  {toast.title}\n                </div>\n              )}\n              {toast.description && (\n                <div className={`text-sm ${\n                  toast.variant === 'destructive' ? 'text-red-700' : 'text-gray-700'\n                }`}>\n                  {toast.description}\n                </div>\n              )}\n            </div>\n          </div>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"absolute right-1 top-1 h-6 w-6 p-0 hover:bg-transparent\"\n            onClick={() => dismiss(toast.id)}\n          >\n            <X className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AALA;;;;;AAOO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEnC,qBACE,8OAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gBAEC,WAAU;gBACV,OAAO;oBACL,iBAAiB,MAAM,OAAO,KAAK,gBAAgB,YAAY;oBAC/D,aAAa,MAAM,OAAO,KAAK,gBAAgB,YAAY;gBAC7D;;kCAEA,8OAAC;wBAAI,WAAU;;4BACZ,MAAM,OAAO,KAAK,8BACjB,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CAEzB,8OAAC;gCAAI,WAAU;;oCACZ,MAAM,KAAK,kBACV,8OAAC;wCAAI,WAAW,CAAC,sBAAsB,EACrC,MAAM,OAAO,KAAK,gBAAgB,iBAAiB,iBACnD;kDACC,MAAM,KAAK;;;;;;oCAGf,MAAM,WAAW,kBAChB,8OAAC;wCAAI,WAAW,CAAC,QAAQ,EACvB,MAAM,OAAO,KAAK,gBAAgB,iBAAiB,iBACnD;kDACC,MAAM,WAAW;;;;;;;;;;;;;;;;;;kCAK1B,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,QAAQ,MAAM,EAAE;kCAE/B,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;eApCV,MAAM,EAAE;;;;;;;;;;AA0CvB", "debugId": null}}]}