'use client'

import { useState, useEffect, useRef } from 'react'
import { createPortal } from 'react-dom'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Cavo } from '@/types'
import {
  Eye,
  Edit,
  Trash2,
  Plus,
  Copy,
  FileText,
  Link,
  Unlink,
  Award,
  Play,
  Pause,
  X,
  Check,
  Download,
  AlertTriangle,
  Wrench,
  Package
} from 'lucide-react'

interface CaviContextMenuPortalProps {
  isOpen: boolean
  position: { x: number; y: number }
  cavo: Cavo | null
  isSelected: boolean
  hasMultipleSelection: boolean
  totalSelectedCount: number
  onAction: (cavo: Cavo, action: string) => void
  onClose: () => void
}

export default function CaviContextMenuPortal({
  isOpen,
  position,
  cavo,
  isSelected,
  hasMultipleSelection,
  totalSelectedCount,
  onAction,
  onClose
}: CaviContextMenuPortalProps) {
  const menuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  if (!isOpen || !cavo) {
    console.log('CaviContextMenuPortal not rendering:', { isOpen, cavo: cavo?.id_cavo })
    return null
  }

  console.log('CaviContextMenuPortal rendering for cavo:', cavo.id_cavo, 'at position:', position)

  const handleAction = (action: string) => {
    onAction(cavo, action)
    onClose()
  }

  const menuItems = [
    // Sezione Gestione Cavi
    {
      section: 'Gestione Cavi',
      items: [
        { icon: Eye, label: 'Visualizza Dettagli', action: 'view_details' },
        { icon: Edit, label: 'Modifica Cavo', action: 'edit' },
        { icon: Trash2, label: 'Elimina Cavo', action: 'delete', destructive: true },
        { icon: Plus, label: 'Aggiungi Nuovo Cavo', action: 'add_new' }
      ]
    },
    // Sezione Comandi
    {
      section: 'Comandi',
      items: [
        { icon: Link, label: 'Gestisci Collegamenti', action: 'manage_connections' },
        { icon: Package, label: 'Gestisci Bobina', action: 'manage_reel' },
        { icon: Award, label: 'Certifica Cavo', action: 'create_certificate' },
        { icon: Play, label: 'Avvia Installazione', action: 'start_installation' },
        { icon: Pause, label: 'Sospendi Installazione', action: 'pause_installation' }
      ]
    },
    // Sezione Generale
    {
      section: 'Generale',
      items: [
        { icon: Copy, label: 'Copia ID', action: 'copy_id' },
        { icon: FileText, label: 'Copia Dettagli', action: 'copy_details' },
        { icon: Download, label: 'Esporta Dati', action: 'export_data' }
      ]
    }
  ]

  // Calcola la posizione del menu per evitare che esca dallo schermo
  const adjustedPosition = {
    x: Math.min(position.x, window.innerWidth - 250),
    y: Math.min(position.y, window.innerHeight - 400)
  }

  return createPortal(
    <div
      ref={menuRef}
      className="fixed z-[9999] bg-white border-2 border-gray-300 rounded-lg shadow-2xl py-2 min-w-[220px] max-w-[300px]"
      style={{
        left: adjustedPosition.x,
        top: adjustedPosition.y,
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)'
      }}
    >
      {/* Header con info cavo */}
      <div className="px-3 py-2 border-b border-gray-100">
        <div className="text-sm font-medium text-gray-900">
          Cavo: {cavo.id_cavo}
        </div>
        {isSelected && (
          <div className="text-xs text-blue-600">
            Selezionato {hasMultipleSelection ? `(${totalSelectedCount} totali)` : ''}
          </div>
        )}
      </div>

      {/* Menu items */}
      {menuItems.map((section, sectionIndex) => (
        <div key={section.section}>
          {sectionIndex > 0 && <hr className="my-1 border-gray-200" />}
          <div className="px-2 py-1">
            <div className="text-xs font-medium text-gray-500 px-2 py-1">
              {section.section}
            </div>
            {section.items.map((item) => (
              <Button
                key={item.action}
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-left h-8 px-2 ${
                  item.destructive 
                    ? 'text-red-600 hover:text-red-700 hover:bg-red-50' 
                    : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                }`}
                onClick={() => handleAction(item.action)}
              >
                <item.icon className="w-4 h-4 mr-2" />
                {item.label}
              </Button>
            ))}
          </div>
        </div>
      ))}
    </div>,
    document.body
  )
}
