'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Loader2, Trash2, AlertTriangle, Info } from 'lucide-react'
import { caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { Cavo } from '@/types'

interface EliminaCavoDialogProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  cantiere?: { id_cantiere: string; nome_cantiere: string } | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export default function EliminaCavoDialog({
  open,
  onClose,
  cavo,
  cantiere: cantiereProp,
  onSuccess,
  onError
}: EliminaCavoDialogProps) {
  const { cantiere: cantiereFromContext } = useAuth()
  const cantiere = cantiereProp || cantiereFromContext

  const [loading, setLoading] = useState(false)
  const [deleteOption, setDeleteOption] = useState<'spare' | 'permanent'>('spare')

  const handleDelete = async () => {
    if (!cavo?.id_cavo) {
      onError('Cavo non selezionato')
      return
    }

    if (!cantiere?.id_cantiere) {
      onError('Cantiere non selezionato')
      return
    }

    try {
      setLoading(true)

      if (deleteOption === 'spare') {
        // Marca come SPARE (flag 3)
        await caviApi.markAsSpare(parseInt(cantiere.id_cantiere), cavo.id_cavo, true)
        onSuccess(`Cavo ${cavo.id_cavo} marcato come SPARE`)
      } else {
        // Eliminazione definitiva
        await caviApi.deleteCavo(parseInt(cantiere.id_cantiere), cavo.id_cavo)
        onSuccess(`Cavo ${cavo.id_cavo} eliminato definitivamente`)
      }

      handleClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\'eliminazione del cavo'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      setDeleteOption('spare')
      onClose()
    }
  }

  if (!cavo) return null

  // Determina se il cavo è installato
  const isInstalled = cavo.metratura_reale > 0 || cavo.stato_installazione !== 'da installare'
  const canPermanentDelete = !isInstalled

  // Informazioni sul cavo
  const cavoInfo = {
    id: cavo.id_cavo,
    tipologia: cavo.tipologia || 'N/A',
    sezione: cavo.sezione || 'N/A',
    metri_teorici: cavo.metri_teorici || 0,
    metri_reali: cavo.metratura_reale || 0,
    stato: cavo.stato_installazione || 'N/A',
    bobina: cavo.id_bobina || 'N/A'
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5" />
            Elimina Cavo: {cavo.id_cavo}
          </DialogTitle>
          <DialogDescription>
            Scegli come gestire l'eliminazione del cavo dal cantiere {cantiere?.nome_cantiere}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informazioni sul cavo */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Informazioni Cavo</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">ID:</span>
                <span className="ml-2 font-medium">{cavoInfo.id}</span>
              </div>
              <div>
                <span className="text-gray-600">Tipologia:</span>
                <span className="ml-2 font-medium">{cavoInfo.tipologia}</span>
              </div>
              <div>
                <span className="text-gray-600">Formazione:</span>
                <span className="ml-2 font-medium">{cavoInfo.sezione}</span>
              </div>
              <div>
                <span className="text-gray-600">Stato:</span>
                <span className="ml-2 font-medium">{cavoInfo.stato}</span>
              </div>
              <div>
                <span className="text-gray-600">Metri Teorici:</span>
                <span className="ml-2 font-medium">{cavoInfo.metri_teorici}</span>
              </div>
              <div>
                <span className="text-gray-600">Metri Installati:</span>
                <span className="ml-2 font-medium">{cavoInfo.metri_reali}</span>
              </div>
              <div className="col-span-2">
                <span className="text-gray-600">Bobina:</span>
                <span className="ml-2 font-medium">{cavoInfo.bobina}</span>
              </div>
            </div>
          </div>

          {/* Opzioni di eliminazione */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Modalità di Eliminazione</h3>
            
            <RadioGroup value={deleteOption} onValueChange={(value) => setDeleteOption(value as 'spare' | 'permanent')}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="spare" id="spare" />
                <Label htmlFor="spare" className="flex-1">
                  <div className="font-medium">Marca come SPARE</div>
                  <div className="text-sm text-gray-600">
                    Il cavo viene marcato come spare/consumato ma rimane nel database per tracciabilità
                  </div>
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <RadioGroupItem 
                  value="permanent" 
                  id="permanent" 
                  disabled={!canPermanentDelete}
                />
                <Label htmlFor="permanent" className={`flex-1 ${!canPermanentDelete ? 'opacity-50' : ''}`}>
                  <div className="font-medium">Eliminazione Definitiva</div>
                  <div className="text-sm text-gray-600">
                    Il cavo viene rimosso completamente dal database
                    {!canPermanentDelete && ' (non disponibile per cavi installati)'}
                  </div>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Avvisi */}
          {isInstalled && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <strong>Cavo Installato:</strong> Questo cavo ha metri installati. 
                Si consiglia di marcarlo come SPARE piuttosto che eliminarlo definitivamente.
              </AlertDescription>
            </Alert>
          )}

          {deleteOption === 'permanent' && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Attenzione:</strong> L'eliminazione definitiva è irreversibile. 
                Tutti i dati del cavo verranno persi permanentemente.
              </AlertDescription>
            </Alert>
          )}

          {deleteOption === 'spare' && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Il cavo verrà marcato come SPARE e non apparirà più nelle liste attive, 
                ma rimarrà nel database per eventuali controlli futuri.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Annulla
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleDelete} 
            disabled={loading}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {loading 
              ? 'Eliminando...' 
              : deleteOption === 'spare' 
                ? 'Marca come SPARE' 
                : 'Elimina Definitivamente'
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
